"use client"

import React, { createContext, useContext, useRef, useState, useCallback, useEffect, useMemo } from 'react';
import { useSearchParams } from "next/navigation";
import { useToast } from "@/components/ui/toast";
import { useAuthProtectedCallback } from "@/components/auth/with-auth";
import useVideoGeneratorStore from "@/store/useVideoGeneratorStore";
import useModelSelectorStore from "@/store/useModelSelectorStore";
import { useEffectList, useTaskPrice } from "../../queries";
import { useInfiniteModels } from "../../hooks/useModelQuery";
import { useSelectedModelFromStore } from "../../hooks/useSelectedModelFromStore";
import { TABS } from '../../constants';
import { useStartToEndModel } from "@/hooks/use-start-to-end-model";
import { useModelConfig, useHasModelConfig } from "@/hooks/use-model-config";

// Constants
export const QUALITY_OPTIONS = {
  STANDARD: "standard",
  HIGH: "high",
};

export const DURATION_OPTIONS = {
  SHORT: "5",
  LONG: "10",
};

export const PROMPT_DEBOUNCE_DELAY = 300; // 300ms

export const UI_TEXT = {
  INSUFFICIENT_CREDITS: "Insufficient Credits",
  CREDITS_MESSAGE:
    "You don't have enough credits to generate this video. You need {0} credits, but you only have {1} credits.",
  CANCEL: "Cancel",
  BUY_CREDITS: "Buy Credits",
};

// 旧的价格计算逻辑，作为备用方案
export const calculateVideoCostFallback = (modelInfo: any, quality: string, duration: string): number => {
  // 如果模型有自定义价格，则使用模型价格
  if (modelInfo && modelInfo.price) {
    return modelInfo.price;
  } else {
    // 兼容旧逻辑，如果模型没有价格字段
    const basePrice = quality === QUALITY_OPTIONS.STANDARD ? 50 : 80;
    const durationMultiplier = duration === DURATION_OPTIONS.SHORT ? 1 : 1.8;
    return Math.round(basePrice * durationMultiplier);
  }
};

// Context type
interface ControlPanelContextType {
  // Model data
  models: any[];
  isLoadingModels: boolean;
  selectedModelId: string;
  setSelectedModelId: (id: string) => void;
  selectedModelInfo: any;
  modelType: string;
  isVeoModel: boolean;
  isVeo3Model: boolean;

  // Effect data
  effects: any[];
  isLoadingEffects: boolean;
  selectedEffectName: string;
  handleEffectSelect: (effectName: string) => void;

  // Tab state
  activeTab: string;
  setActiveTab: (tab: string) => void;

  // Video generator state
  creativityLevel: number;
  setCreativityLevel: (level: number) => void;
  quality: string;
  duration: string;
  setDuration: (duration: string) => void;
  aspectRatio: string;
  setAspectRatio: (ratio: string) => void;
  globalPrompt: string;
  debouncedSetPrompt: (prompt: string) => void;
  negativePrompt: string;
  setNegativePrompt: (prompt: string) => void;
  steps: number;
  setSteps: (steps: number) => void;
  seed: number;
  setSeed: (seed: number) => void;

  // Image handling
  images: string[];
  isLoadingPresignedUrl: boolean;
  allowReferenceImage: boolean;
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  referenceImageAreaRef: React.RefObject<HTMLDivElement | null>;
  handleUploadClick: () => void;
  authenticatedHandleImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeImage: (index: number) => void;
  imageSizeError: string | null;
  setImageSizeError: (error: string | null) => void;

  // Start-to-end frame model support
  isStartToEndModel: boolean;
  startImages: string[];
  endImages: string[];
  isUploadingStartToEnd: boolean;
  startFileInputRef: React.RefObject<HTMLInputElement | null>;
  endFileInputRef: React.RefObject<HTMLInputElement | null>;
  handleStartImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleEndImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeStartImage: (index: number) => void;
  removeEndImage: (index: number) => void;
  setStartImages: (images: string[]) => void;
  setEndImages: (images: string[]) => void;

  // Generation
  isGenerating: boolean;
  error: string | null;
  videoCost: number;
  isLoadingTaskPrice: boolean;
  handleGenerateVideo: () => void;

  showInsufficientCreditsDialog: boolean;
  setShowInsufficientCreditsDialog: (show: boolean) => void;

  // Dynamic model parameters
  hasAdvancedConfig: boolean;
  isLoadingModelConfig: boolean;
  dynamicParams: Record<string, any>;
  setDynamicParams: (params: Record<string, any>) => void;
  updateDynamicParam: (key: string, value: any) => void;
  dynamicParamErrors: Record<string, string>;
  setDynamicParamErrors: (errors: Record<string, string>) => void;
}

// Create context
const ControlPanelContext = createContext<ControlPanelContextType | undefined>(undefined);

// Provider component
export const ControlPanelProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const referenceImageAreaRef = useRef<HTMLDivElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Start-to-end frame model refs
  const startFileInputRef = useRef<HTMLInputElement>(null);
  const endFileInputRef = useRef<HTMLInputElement>(null);

  // URL params
  const searchParams = useSearchParams();
  const urlModelId = searchParams?.get("modelId") || null;

  // Data fetching
  const { data: effectData, isLoading: isLoadingEffects } = useEffectList();
  // @ts-ignore
  const effects = (effectData?.effects || []) as Effect[];

  // 使用 useInfiniteModels 获取所有分页的模型数据
  const {
    data: infiniteModelsData,
    isLoading: isLoadingInfiniteModels
  } = useInfiniteModels();

  // 合并所有页面的模型数据
  const models = useMemo(() => {
    if (!infiniteModelsData) return [];
    return infiniteModelsData.pages.flatMap(page => page.models);
  }, [infiniteModelsData]);

  // 从 ModelSelectorStore 获取选中的模型ID
  const selectedModelId = useModelSelectorStore((state) => state.selectedModelId);
  const setSelectedModelId = useModelSelectorStore((state) => state.setSelectedModelId);

  // 使用 useSelectedModelFromStore 获取完整的模型数据
  const {
    selectedModel: selectedModelInfo,
    isLoading: isLoadingSelectedModel
  } = useSelectedModelFromStore();

  // 合并加载状态
  const isLoadingModels = isLoadingInfiniteModels || isLoadingSelectedModel;

  // 其他状态
  const [showInsufficientCreditsDialog, setShowInsufficientCreditsDialog] = useState(false);
  const [imageSizeError, setImageSizeError] = useState<string | null>(null);

  // 动态模型参数状态
  const [dynamicParams, setDynamicParams] = useState<Record<string, any>>({});
  const [dynamicParamErrors, setDynamicParamErrors] = useState<Record<string, string>>({});

  // Start-to-end frame model hook
  const startToEndModel = useStartToEndModel(setImageSizeError);

  // 模型配置查询
  const { data: hasAdvancedConfig, isLoading: isLoadingHasConfig } = useHasModelConfig(
    selectedModelInfo?.storage_path || null
  );
  const { data: modelConfig, isLoading: isLoadingModelConfig } = useModelConfig(
    hasAdvancedConfig ? selectedModelInfo?.storage_path || null : null
  );

  // 动态参数处理函数
  const updateDynamicParam = useCallback((key: string, value: any) => {
    setDynamicParams(prev => ({ ...prev, [key]: value }));
    // 清除该参数的错误
    setDynamicParamErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[key];
      return newErrors;
    });
  }, []);

  // 当模型配置加载完成时，设置默认参数
  useEffect(() => {
    if (modelConfig && Object.keys(dynamicParams).length === 0) {
      const defaultParams: Record<string, any> = {};
      Object.entries(modelConfig.input).forEach(([paramName, paramConfig]) => {
        if (paramConfig.default !== undefined) {
          defaultParams[paramName] = paramConfig.default;
        }
      });
      if (Object.keys(defaultParams).length > 0) {
        setDynamicParams(defaultParams);
      }
    }
  }, [modelConfig, dynamicParams]);

  // 当切换模型时，清空动态参数
  useEffect(() => {
    setDynamicParams({});
    setDynamicParamErrors({});
  }, [selectedModelId]);

  // Store state - 使用全局store的activeTab
  const { activeTab, setActiveTab } = useModelSelectorStore();

  // Video generator store actions
  const setCreativityLevel = useVideoGeneratorStore(state => state.setCreativityLevel);
  const setDuration = useVideoGeneratorStore(state => state.setDuration);
  const setAspectRatio = useVideoGeneratorStore(state => state.setAspectRatio);
  const setPrompt = useVideoGeneratorStore(state => state.setPrompt);
  const setNegativePrompt = useVideoGeneratorStore(state => state.setNegativePrompt);
  const setSteps = useVideoGeneratorStore(state => state.setSteps);
  const setSeed = useVideoGeneratorStore(state => state.setSeed);
  const addImage = useVideoGeneratorStore(state => state.addImage);
  const removeImage = useVideoGeneratorStore(state => state.removeImage);
  const generateVideoAction = useVideoGeneratorStore(state => state.generateVideo);
  const setSelectedEffectName = useVideoGeneratorStore(state => state.setSelectedEffectName);
  const setGenerationType = useVideoGeneratorStore(state => state.setGenerationType);

  // Video generator store state
  const creativityLevel = useVideoGeneratorStore(state => state.creativityLevel);
  const quality = useVideoGeneratorStore(state => state.quality);
  const duration = useVideoGeneratorStore(state => state.duration);
  const aspectRatio = useVideoGeneratorStore(state => state.aspectRatio);
  const globalPrompt = useVideoGeneratorStore(state => state.prompt);
  const negativePrompt = useVideoGeneratorStore(state => state.negativePrompt);
  const steps = useVideoGeneratorStore(state => state.steps);
  const seed = useVideoGeneratorStore(state => state.seed);
  const images = useVideoGeneratorStore(state => state.images);
  const isGenerating = useVideoGeneratorStore(state => state.isGenerating);
  const isLoadingPresignedUrl = useVideoGeneratorStore(state => state.isLoadingPresignedUrl);
  const error = useVideoGeneratorStore(state => state.error);
  const selectedEffectName = useVideoGeneratorStore(state => state.selectedEffectName);

  const { success, error: toastError } = useToast();

  // 从模型信息中获取模型类型
  const modelType = useMemo(() => {
    return selectedModelInfo?.model_type?.toLowerCase() || '';
  }, [selectedModelInfo]);

  // 检查是否为veo3模型（只有veo3才有8s时间限制）
  const isVeo3Model = useMemo(() => {
    return selectedModelInfo?.storage_path === 'fal-ai/veo3';
  }, [selectedModelInfo]);

  // 检查是否为veo模型（包括veo、veo3等所有veo系列模型）
  const isVeoModel = useMemo(() => {
    if (!selectedModelInfo?.storage_path) return false;
    return selectedModelInfo.storage_path.toLowerCase().includes('veo');
  }, [selectedModelInfo]);

  // 检查是否为首尾帧模型
  const isStartToEndModel = useMemo(() => {
    return selectedModelInfo?.default_config?.start_to_end === true;
  }, [selectedModelInfo]);

  const allowReferenceImage = useMemo(() => {
    if (activeTab === TABS.EFFECT) {
      return true;
    }

    if (modelType === 'image-to-video' || modelType === 'video-to-video') {
      return true;
    }

    return selectedModelInfo?.default_config?.allow_reference_image === true;
  }, [activeTab, modelType, selectedModelInfo]);

  // Get task price
  const { data: taskPriceData, isLoading: isLoadingTaskPrice } = useTaskPrice(selectedModelId, duration);

  const videoCost = useMemo(() => {
    if (taskPriceData && taskPriceData.price) {
      return taskPriceData.price;
    }
    return calculateVideoCostFallback(selectedModelInfo, quality, duration);
  }, [taskPriceData, selectedModelInfo, quality, duration]);

  // Debounced prompt update
  const debouncedSetPrompt = useCallback(
    (newPrompt: string) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(() => {
        setPrompt(newPrompt);
      }, PROMPT_DEBOUNCE_DELAY);
    },
    [setPrompt]
  );

  // 处理 URL 中的模型 ID
  useEffect(() => {
    if (urlModelId) {
      // 直接设置 store 中的选中模型 ID，useSelectedModelFromStore 会处理加载
      setSelectedModelId(urlModelId);
    }
  }, [urlModelId, setSelectedModelId]);

  // 当选择veo3模型时，自动设置参数限制
  useEffect(() => {
    if (isVeo3Model) {
      // 自动设置aspect_ratio为16:9
      if (aspectRatio !== '16:9') {
        setAspectRatio('16:9');
      }
      // 自动设置duration为8s
      if (duration !== '8') {
        setDuration('8');
      }
    } else {
      // 当从veo3模型切换到非veo3模型时，如果当前时长是8s，则调整为5s
      if (duration === '8') {
        setDuration('5');
      }
    }
  }, [isVeo3Model, aspectRatio, duration, setAspectRatio, setDuration]);

  // 监听设置首尾帧图片的事件（用于remix功能）
  useEffect(() => {
    const handleSetStartToEndImages = (event: CustomEvent) => {
      const { startImageUrl, endImageUrl } = event.detail;
      if (startImageUrl && endImageUrl) {
        startToEndModel.setStartImages([startImageUrl]);
        startToEndModel.setEndImages([endImageUrl]);
      }
    };

    window.addEventListener('setStartToEndImages', handleSetStartToEndImages as EventListener);

    return () => {
      window.removeEventListener('setStartToEndImages', handleSetStartToEndImages as EventListener);
    };
  }, [startToEndModel]);

  // Media upload handlers (image or video)
  const handleImageUpload = useCallback(async (file: File) => {
    // 清除之前的错误
    setImageSizeError(null);

    // 检查文件类型，支持图片和视频
    const isImage = file.type.startsWith("image/");
    const isVideo = file.type.startsWith("video/");

    if (!isImage && !isVideo) {
      toastError("File Type Error", "Only image or video files are allowed");
      return;
    }

    // 根据模型类型验证上传的文件类型
    if (modelType === 'image-to-video' && !isImage) {
      toastError("File Type Error", "This model only supports image files");
      return;
    }

    if (modelType === 'video-to-video' && !isVideo) {
      toastError("File Type Error", "This model only supports video files");
      return;
    }

    const maxSize = 50 * 1024 * 1024; // 增加到50MB以支持视频文件
    if (file.size > maxSize) {
      toastError("File Size Error", `The ${isImage ? 'image' : 'video'} size cannot exceed 50MB`);
      return;
    }

    // 检查图片尺寸（仅对图片进行验证）
    let hasSizeError = false;
    if (isImage) {
      const checkImageDimensions = () => {
        return new Promise<boolean>((resolve) => {
          const img = new Image();
          img.onload = () => {
            if (img.width < 300 || img.height < 300) {
              setImageSizeError(`Image dimensions must be at least 300×300 pixels (current: ${img.width}×${img.height})`);
              console.log(`Image size validation failed: ${img.width}×${img.height}`);
              hasSizeError = true;
              resolve(false);
            } else {
              resolve(true);
            }
          };
          img.onerror = () => {
            setImageSizeError("Failed to load image for dimension validation");
            hasSizeError = true;
            resolve(false);
          };
          img.src = URL.createObjectURL(file);
        });
      };

      await checkImageDimensions();
    }

    // 即使图片尺寸不符合要求，也要显示图片，以便用户看到错误提示
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        addImage(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);

    // 如果图片尺寸不符合要求，不继续上传到服务器
    if (hasSizeError) {
      console.log("Skipping server upload due to image size error");
      return;
    }

    try {
      // 设置上传状态为true，使生成按钮显示为加载状态
      useVideoGeneratorStore.getState().isLoadingPresignedUrl = true;

      const response = await fetch('/api/upload/presign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get upload URL');
      }

      const { data } = await response.json();

      const uploadResponse = await fetch(data.url, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      if (!uploadResponse.ok) {
        throw new Error(`Upload ${isImage ? 'image' : 'video'} failed`);
      }

      removeImage(0);
      addImage(data.publicUrl);

      // 根据文件类型和模型类型设置生成类型
      if (isImage && modelType === 'image-to-video') {
        setGenerationType('image-to-video');
      } else if (isVideo && modelType === 'video-to-video') {
        setGenerationType('video-to-video');
      } else if (modelType !== 'image-to-video' && modelType !== 'video-to-video' && modelType !== '') {
        setGenerationType('text-to-video');
      }

      // 上传完成后，设置上传状态为false
      useVideoGeneratorStore.getState().isLoadingPresignedUrl = false;
    } catch (error) {
      console.error('Upload media error:', error);
      toastError("Upload Failed", "Upload failed, please try again");

      if (useVideoGeneratorStore.getState().images.length > 0) {
        removeImage(0);
      }

      // 发生错误时，也要设置上传状态为false
      useVideoGeneratorStore.getState().isLoadingPresignedUrl = false;
    }
  }, [addImage, removeImage, modelType, setGenerationType]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    handleImageUpload(files[0]);
  }, [handleImageUpload]);

  const authenticatedHandleImageUpload = useAuthProtectedCallback(
    handleFileInputChange,
    "upload_image"
  );

  // 重写 removeImage 函数，确保在删除图片时清除尺寸错误
  const handleRemoveImage = useCallback((index: number) => {
    setImageSizeError(null);
    removeImage(index);
  }, [removeImage, setImageSizeError]);

  const handlePaste = useAuthProtectedCallback(
    useCallback((e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;

      if (!allowReferenceImage) return;

      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          const file = items[i].getAsFile();
          if (file) {
            handleImageUpload(file);
            break;
          }
        }
      }
    }, [allowReferenceImage, handleImageUpload]),
    "paste_image"
  );

  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Start-to-end frame image upload handlers
  const handleStartImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    startToEndModel.handleStartImageUpload(files[0]).catch((error) => {
      toastError("Upload Failed", "Upload failed: " + error.message);
    });
  }, [startToEndModel, toastError]);

  const handleEndImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    startToEndModel.handleEndImageUpload(files[0]).catch((error) => {
      toastError("Upload Failed", "Upload failed: " + error.message);
    });
  }, [startToEndModel, toastError]);

  const removeStartImage = useCallback((_index: number) => {
    startToEndModel.removeStartImage();
  }, [startToEndModel]);

  const removeEndImage = useCallback((_index: number) => {
    startToEndModel.removeEndImage();
  }, [startToEndModel]);

  useEffect(() => {
    if (allowReferenceImage) {
      document.addEventListener('paste', handlePaste);
    }

    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [allowReferenceImage, handlePaste]);

  // Effect selection handler
  const handleEffectSelect = useCallback((effectName: string) => {
    const selectedEffect = effects.find(effect => effect.name === effectName);
    setSelectedEffectName(effectName);

    if (selectedEffect && selectedEffect.trigger_words && selectedEffect.trigger_words.length > 0) {
      console.log('Effect trigger words:', selectedEffect.trigger_words);
    }
  }, [effects, setSelectedEffectName]);

  // Generate video handler
  const handleGenerateVideoInternal = useCallback(async () => {
    // 使用 selectedModelInfo 而不是从 models 中查找
    if (!selectedModelInfo) {
      toastError("Please select a model first");
      return;
    }

    const model = selectedModelInfo;

    // 如果模型支持高级配置，验证动态参数
    if (hasAdvancedConfig && modelConfig) {
      try {
        const { modelConfigApi } = await import('@/lib/api/model-config');
        const validation = await modelConfigApi.validateModelParams({
          modelId: model.storage_path || '',
          params: dynamicParams
        });

        if (!validation.isValid) {
          setDynamicParamErrors(
            validation.errors.reduce((acc, error, index) => {
              acc[`error_${index}`] = error;
              return acc;
            }, {} as Record<string, string>)
          );
          toastError("Parameter validation failed: " + validation.errors.join(', '));
          return;
        }

        // 清除参数错误
        setDynamicParamErrors({});
      } catch (error) {
        console.warn('Failed to validate dynamic parameters:', error);
        // 继续执行，不阻止生成
      }
    }

    if (isLoadingPresignedUrl) {
      toastError("Media is uploading, please wait until it completes");
      return;
    }

    // 检查图片尺寸错误
    if (imageSizeError) {
      toastError("Cannot generate: " + imageSizeError);
      console.error("Generation blocked due to image size error:", imageSizeError);
      return;
    }

    // Validate model type and input
    if (isStartToEndModel) {
      // 首尾帧模型验证
      setGenerationType('image-to-video');

      const validation = startToEndModel.validateStartToEndModel();
      if (!validation.isValid) {
        toastError(validation.errorMessage!);
        return;
      }
    } else if (modelType === 'text-to-video') {
      setGenerationType('text-to-video');

      if (!globalPrompt.trim()) {
        toastError("Please enter a prompt describing the video you want to generate");
        return;
      }
    } else if (modelType === 'image-to-video') {
      setGenerationType('image-to-video');

      if (images.length === 0) {
        toastError("Please upload a reference image");
        return;
      }

      if (images[0].startsWith('data:')) {
        toastError("Image is processing, please wait for upload to complete");
        return;
      }
    } else if (modelType === 'video-to-video') {
      setGenerationType('video-to-video');

      if (images.length === 0) {
        toastError("Please upload a reference video");
        return;
      }

      if (images[0].startsWith('data:')) {
        toastError("Video is processing, please wait for upload to complete");
        return;
      }
    }

    // 5. Find selected effect
    const selectedEffect = selectedEffectName ?
      effects.find((effect) => effect.name === selectedEffectName) :
      null;

    // 6. Call generate method
    let result;
    if (isStartToEndModel) {
      // 首尾帧模型使用自定义逻辑
      result = await startToEndModel.generateStartToEndVideo(model, selectedEffect);
    } else {
      // 其他模型使用原有逻辑，传递动态参数
      result = await generateVideoAction(model, selectedEffect, dynamicParams);
    }

    // 7. Handle result
    if (result) {
      success("Video generation task started!");
      // 刷新任务列表
      window.dispatchEvent(new CustomEvent('refreshTaskList'));
    } else {
      const currentError = useVideoGeneratorStore.getState().error;
      if (currentError) {
        toastError(currentError);
      }
    }
  }, [
    selectedModelInfo,
    selectedEffectName,
    effects,
    generateVideoAction,
    success,
    toastError,
    modelType,
    setGenerationType,
    images,
    globalPrompt,
    isLoadingPresignedUrl,
    isStartToEndModel,
    startToEndModel,
    hasAdvancedConfig,
    modelConfig,
    dynamicParams,
    setDynamicParamErrors
  ]);

  const handleGenerateVideo = useAuthProtectedCallback(handleGenerateVideoInternal, "generate_video");

  // Context value
  const contextValue: ControlPanelContextType = {
    // Model data
    models,
    isLoadingModels,
    selectedModelId,
    setSelectedModelId,
    selectedModelInfo,
    modelType,
    isVeoModel,
    isVeo3Model,

    // Effect data
    effects,
    isLoadingEffects,
    selectedEffectName,
    handleEffectSelect,

    // Tab state
    activeTab,
    setActiveTab,

    // Video generator state
    creativityLevel,
    setCreativityLevel,
    quality,
    duration,
    setDuration,
    aspectRatio,
    setAspectRatio,
    globalPrompt,
    debouncedSetPrompt,
    negativePrompt,
    setNegativePrompt,
    steps,
    setSteps,
    seed,
    setSeed,

    // Image handling
    images,
    isLoadingPresignedUrl,
    allowReferenceImage,
    fileInputRef,
    referenceImageAreaRef,
    handleUploadClick,
    authenticatedHandleImageUpload,
    removeImage: handleRemoveImage,
    imageSizeError,
    setImageSizeError,

    // Start-to-end frame model support
    isStartToEndModel,
    startImages: startToEndModel.startImages,
    endImages: startToEndModel.endImages,
    isUploadingStartToEnd: startToEndModel.isUploading,
    startFileInputRef,
    endFileInputRef,
    handleStartImageUpload,
    handleEndImageUpload,
    removeStartImage,
    removeEndImage,
    setStartImages: startToEndModel.setStartImages,
    setEndImages: startToEndModel.setEndImages,

    // Generation
    isGenerating,
    error,
    videoCost,
    isLoadingTaskPrice,
    handleGenerateVideo,

    // Credits
    showInsufficientCreditsDialog,
    setShowInsufficientCreditsDialog,

    // Dynamic model parameters
    hasAdvancedConfig: !!hasAdvancedConfig,
    isLoadingModelConfig: isLoadingModelConfig || isLoadingHasConfig,
    dynamicParams,
    setDynamicParams,
    updateDynamicParam,
    dynamicParamErrors,
    setDynamicParamErrors,
  };

  return (
    <ControlPanelContext.Provider value={contextValue}>
      {children}
    </ControlPanelContext.Provider>
  );
};

// Hook for using the context
export const useControlPanel = () => {
  const context = useContext(ControlPanelContext);
  if (context === undefined) {
    throw new Error('useControlPanel must be used within a ControlPanelProvider');
  }
  return context;
};

