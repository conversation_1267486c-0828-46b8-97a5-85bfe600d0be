"use client"

import { useEffect, useRef, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { VideoTask } from "@/types/video-task"
import { X, Download, Share, Sparkles } from "lucide-react"
import useVideoTasksStore from "@/store/useVideoTasksStore"
import { useRouter } from "next/navigation"
import { downloadVideo } from "@/lib/utils/downloadVideo"

interface HistoryVideoModalProps {
    taskId: string | null
    onClose: () => void
    isOpen: boolean
}

export function HistoryVideoModal({ taskId, onClose, isOpen }: HistoryVideoModalProps) {
    const modalRef = useRef<HTMLDivElement>(null)
    const router = useRouter()
    const videoRef = useRef<HTMLVideoElement>(null)
    const [isPlaying, setIsPlaying] = useState(false)
    const [isDownloading, setIsDownloading] = useState(false)

    // 使用视频任务store获取任务
    const { setSelectedTask, videoTasks } = useVideoTasksStore()
    const [videoTask, setVideoTask] = useState<VideoTask | null>(null)
    const [isLoading, setIsLoading] = useState(true)

    // 组件加载时获取数据
    useEffect(() => {
        if (!taskId || !isOpen) return;

        const loadTask = async () => {
            setIsLoading(true)
            try {
                // 尝试从现有任务列表中查找
                const existingTask = videoTasks.find(task => task.id === taskId)
                if (existingTask) {
                    setVideoTask(existingTask)
                } else {
                    // 如果找不到，使用setSelectedTask方法获取
                    const task = setSelectedTask(taskId)
                    if (task) {
                        setVideoTask(task)
                    }
                }
            } catch (err) {
                console.error("Error loading video task:", err)
            } finally {
                setIsLoading(false)
            }
        }

        loadTask()

        // 更新浏览器历史记录，但不导航
        const currentUrl = window.location.href
        window.history.pushState({ taskId, previousUrl: currentUrl }, "", `/create?task=${taskId}`)

        // 添加历史记录弹出事件监听器
        const handlePopState = () => {
            onClose()
        }
        window.addEventListener("popstate", handlePopState)

        // 禁止背景滚动
        document.body.style.overflow = 'hidden'

        return () => {
            window.removeEventListener("popstate", handlePopState)
            // 恢复背景滚动
            document.body.style.overflow = ''
            setVideoTask(null)
        }
    }, [taskId, isOpen, setSelectedTask, videoTasks, onClose])

    // 点击外部关闭弹窗
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside)
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside)
        }
    }, [isOpen, onClose])

    // 按ESC关闭弹窗
    useEffect(() => {
        const handleEscKey = (event: KeyboardEvent) => {
            if (event.key === "Escape") {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener("keydown", handleEscKey)
        }

        return () => {
            document.removeEventListener("keydown", handleEscKey)
        }
    }, [isOpen, onClose])

    // 处理视频播放/暂停
    const togglePlay = () => {
        if (!videoRef.current) return

        if (isPlaying) {
            videoRef.current.pause()
        } else {
            videoRef.current.play().catch(err => console.error("Failed to play video:", err))
        }
        setIsPlaying(!isPlaying)
    }

    // 处理下载
    const handleDownload = async () => {
        if (!videoTask?.output_result?.video_url || isDownloading) return

        setIsDownloading(true)
        
        try {
            // 生成文件名
            const prompt = videoTask.input_params?.prompt || "video"
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
            const filename = `Reelmind-${prompt.slice(0, 10)}-${timestamp}.mp4`
            
            const success = await downloadVideo(videoTask.output_result.video_url, filename)
            
            if (success) {
                console.log('Video download completed successfully')
            }
        } catch (error) {
            console.error('Video download failed:', error)
        } finally {
            setIsDownloading(false)
        }
    }

    // 处理分享
    const handleShare = () => {
        // 在这里实现分享逻辑
        console.log("Share feature coming soon")
    }

    // 处理Remix
    const handleRemix = () => {
        if (!videoTask) return
        router.push(`/create?remix=${videoTask.id}`)
        onClose()
    }

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    ref={modalRef}
                    className="fixed inset-0 z-[100] bg-black/90 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <motion.div
                        className="relative w-full h-full overflow-hidden px-4 pt-6 pb-4"
                        initial={{ scale: 0.95, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.95, opacity: 0 }}
                        transition={{ duration: 0.15 }}
                    >
                        {/* 关闭按钮 */}
                        <button
                            onClick={onClose}
                            className="absolute top-4 right-4 z-50 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
                            title="Close modal"
                        >
                            <X size={20} />
                        </button>

                        {/* 加载状态 */}
                        {isLoading && (
                            <div className="w-full h-full flex items-center justify-center">
                                <div className="flex flex-col items-center">
                                    <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
                                    <div className="text-white text-lg">Loading video...</div>
                                </div>
                            </div>
                        )}

                        {/* 错误状态 - 任务不存在或无法访问 */}
                        {!isLoading && !videoTask && (
                            <div className="w-full h-full flex items-center justify-center">
                                <div className="max-w-md p-6 bg-card rounded-xl">
                                    <h2 className="text-xl font-semibold mb-2 text-red-500">Video not found</h2>
                                    <p className="text-muted-foreground mb-4">
                                        The requested video cannot be found or is currently unavailable.
                                    </p>
                                    <button
                                        onClick={onClose}
                                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* 视频内容 */}
                        {!isLoading && videoTask && videoTask.status === "completed" && videoTask.output_result?.video_url && (
                            <div className="w-full h-full flex flex-col md:flex-row gap-4">
                                {/* 视频区域 */}
                                <div className="flex-1 bg-foreground/10 backdrop-blur-md rounded-xl overflow-hidden flex items-center justify-center relative">
                                    <div
                                        className="w-full h-full flex items-center justify-center cursor-pointer"
                                        onClick={togglePlay}
                                    >
                                        <video
                                            ref={videoRef}
                                            src={videoTask.output_result.video_url}
                                            className={`max-h-full ${videoTask.input_params.ratio === "9:16" ? "w-auto h-full" : videoTask.input_params.ratio === "1:1" ? "max-w-[80%] max-h-[80%]" : "w-full"}`}
                                            autoPlay
                                            playsInline
                                            loop
                                            muted
                                            onPlay={() => setIsPlaying(true)}
                                            onPause={() => setIsPlaying(false)}
                                        />

                                        {/* 播放按钮覆盖层 */}
                                        {!isPlaying && (
                                            <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                                                <div className="w-16 h-16 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center">
                                                    <div className="w-0 h-0 border-t-8 border-t-transparent border-l-16 border-l-white border-b-8 border-b-transparent ml-1"></div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* 详情区域 */}
                                <div className="w-full md:w-80 bg-card/20 backdrop-blur-md rounded-xl overflow-hidden flex flex-col">
                                    {/* 详情头部 */}
                                    <div className="p-4 border-b border-white/10">
                                        <h2 className="text-lg font-medium text-white mb-2">Video Details</h2>
                                        <p className="text-white/80 text-sm line-clamp-3">{videoTask.input_params.prompt}</p>
                                    </div>

                                    {/* 详情主体 */}
                                    <div className="p-4 flex-1 overflow-y-auto">
                                        <div className="space-y-4">
                                            {/* 视频信息 */}
                                            <div>
                                                <h3 className="text-sm font-medium text-white/70 mb-2">Video Information</h3>
                                                <div className="grid grid-cols-2 gap-2 text-sm">
                                                    <div className="text-white/60">Model</div>
                                                    <div className="text-white">
                                                        {videoTask.input_params.model_name}
                                                    </div>
                                                    <div className="text-white/60">Resolution</div>
                                                    <div className="text-white">
                                                        {videoTask.input_params.definition || "480P"}
                                                    </div>
                                                    <div className="text-white/60">Aspect Ratio</div>
                                                    <div className="text-white">
                                                        {videoTask.input_params.ratio}
                                                    </div>
                                                    <div className="text-white/60">Duration</div>
                                                    <div className="text-white">
                                                        {videoTask.input_params.duration || "5s"}
                                                    </div>
                                                </div>
                                            </div>

                                            {/* 高级设置 */}
                                            {(videoTask.input_params.steps || videoTask.input_params.seed || videoTask.input_params.negative_prompt) && (
                                                <div>
                                                    <h3 className="text-sm font-medium text-white/70 mb-2">Advanced Settings</h3>
                                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                                        {videoTask.input_params.steps && (
                                                            <>
                                                                <div className="text-white/60">Steps</div>
                                                                <div className="text-white">{videoTask.input_params.steps}</div>
                                                            </>
                                                        )}
                                                        {videoTask.input_params.seed && (
                                                            <>
                                                                <div className="text-white/60">Seed</div>
                                                                <div className="text-white">{videoTask.input_params.seed}</div>
                                                            </>
                                                        )}
                                                    </div>

                                                    {videoTask.input_params.negative_prompt && (
                                                        <div className="mt-2">
                                                            <div className="text-white/60 text-sm mb-1">Negative Prompt</div>
                                                            <div className="text-white/90 text-sm bg-black/30 p-2 rounded-md">
                                                                {videoTask.input_params.negative_prompt}
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            {/* 创建日期 */}
                                            <div>
                                                <h3 className="text-sm font-medium text-white/70 mb-2">Creation Date</h3>
                                                <div className="text-white/90 text-sm">
                                                    {new Date(videoTask.created_at).toLocaleString()}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 操作按钮 */}
                                    <div className="p-4 bg-black/20 flex items-center gap-2">
                                        <button
                                            onClick={handleRemix}
                                            className="flex-1 flex items-center justify-center gap-2 px-4 py-2.5 
                                            bg-white/10 hover:bg-white/20 
                                            backdrop-blur-md 
                                            text-white text-sm font-medium rounded-md transition-all"
                                            title="Create a new video using this prompt"
                                        >
                                            <Sparkles size={16} />
                                            Remix
                                        </button>

                                        <button
                                            onClick={handleDownload}
                                            disabled={isDownloading}
                                            className="flex items-center justify-center gap-2 px-4 py-2.5 
                                            bg-white/10 hover:bg-white/20 
                                            backdrop-blur-md 
                                            text-white text-sm font-medium rounded-md transition-all
                                            disabled:opacity-50 disabled:cursor-not-allowed"
                                            title={isDownloading ? "Downloading..." : "Download this video"}
                                        >
                                            {isDownloading ? (
                                                <>
                                                    <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin" />
                                                    Downloading...
                                                </>
                                            ) : (
                                                <>
                                                    <Download size={16} />
                                                    Download
                                                </>
                                            )}
                                        </button>

                                        <button
                                            onClick={handleShare}
                                            className="flex items-center justify-center w-10 h-10 
                                            bg-white/10 hover:bg-white/20 
                                            backdrop-blur-md 
                                            text-white 
                                            rounded-md transition-all"
                                            title="Share this video"
                                        >
                                            <Share size={16} />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    )
} 