import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';
import type { VideoTask, TaskStatus } from '@/types/video-task';
import type { APIGenType, APIDefinition, APIDuration, APIRatio } from '@/types/video';

// 视频生成请求参数
export interface VideoGenerationRequest {
    prompt: string;
    gen_type: APIGenType;
    negative_prompt?: string;
    guidance_scale?: number;
    steps?: number;
    seed?: number;
    definition?: APIDefinition;
    duration?: APIDuration;
    ratio?: APIRatio;
    model_id?: string;
    refer_img_url?: string | null;
    video_url?: string | null;
    effect_name?: string;
    trigger_words?: string[];
    model_metadata?: {
        duration_estimate?: number; // 预计处理时间（分钟）
        [key: string]: any;
    };
    // 首尾帧模型支持
    start_image_url?: string | null;
    end_image_url?: string | null;
    // 动态模型参数支持
    dynamic_params?: Record<string, any>;
}

// 任务价格请求参数
export interface TaskPriceRequest {
    model_id: string;
    duration?: APIDuration;
}

// 任务价格响应数据
export interface TaskPriceResponse {
    model_id: string;
    price: number;
    duration: APIDuration;
    model_name?: string;
    currency?: string;
}

// 提示增强请求参数
export interface EnhancePromptRequest {
    prompt: string;
}

// 提示增强响应数据
export interface EnhancePromptResponse {
    enhancedPrompt: string;
}

// 视频任务列表响应
export interface VideoTasksResponse {
    tasks: VideoTask[];
    total: number;
}

/**
 * 视频生成API服务
 */
export const generationApi = {
    /**
     * 创建视频生成任务
     */
    createTask: async (params: VideoGenerationRequest): Promise<VideoTask> => {
        const response = await apiClient.post<ApiResponse<VideoTask>>(
            API_CONFIG.ENDPOINTS.GENERATION.CREATE_TASK,
            params
        );
        return response.data;
    },

    /**
     * 获取用户任务列表
     */
    getUserTasks: async (
        status?: string,
        limit: number = 10,
        offset: number = 0
    ): Promise<VideoTasksResponse> => {
        const params: Record<string, string> = {};

        if (status) params.status = status;
        params.limit = limit.toString();
        params.offset = offset.toString();

        const response = await apiClient.get<ApiResponse<VideoTasksResponse>>(
            API_CONFIG.ENDPOINTS.GENERATION.GET_USER_TASKS,
            { params }
        );
        return response.data;
    },

    /**
     * 获取任务详情
     */
    getTaskDetail: async (taskId: string): Promise<VideoTask> => {
        const response = await apiClient.get<ApiResponse<VideoTask>>(
            `${API_CONFIG.ENDPOINTS.GENERATION.GET_TASK_DETAIL}/${taskId}`
        );
        return response.data;
    },

    /**
     * 增强提示词API
     * @param prompt 原始提示词
     * @returns 增强后的提示词
     */
    async enhancePrompt(prompt: string): Promise<EnhancePromptResponse> {
        const response = await apiClient.post<ApiResponse<EnhancePromptResponse>>(
            '/generation/enhance-prompt/video',
            { prompt }
        );
        return response.data;
    },

    /**
     * 获取任务价格
     * @param modelId 模型ID
     * @param duration 视频时长
     * @returns 任务价格信息
     */
    async getTaskPrice(modelId: string, duration?: APIDuration): Promise<TaskPriceResponse> {
        const response = await apiClient.post<ApiResponse<TaskPriceResponse>>(
            API_CONFIG.ENDPOINTS.GENERATION.GET_TASK_PRICE,
            {
                model_id: modelId,
                duration
            }
        );
        return response.data;
    }
};