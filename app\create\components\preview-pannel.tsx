import { <PERSON><PERSON><PERSON>, Maximize2, Minimize2, Share, Download, Clock, Users } from "lucide-react"
import type { AspectRatio } from "@/types/video"
import type { VideoTask } from "@/types/video-task"
import { useState, useRef, useEffect } from "react"
import { PublishDialog } from "./publish-dialog"
import { cn } from "@/lib/utils"
import { useQueueInfo } from "@/hooks/use-queue-info"
import useQueueInfoStore from "@/store/useQueueInfoStore"
import { downloadVideo } from "@/lib/utils/downloadVideo"

interface PreviewPanelProps {
    isGenerating: boolean
    aspectRatio: AspectRatio
    currentTask: VideoTask | null
}

export function PreviewPanel({ isGenerating, aspectRatio, currentTask }: PreviewPanelProps) {
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isVideoLoaded, setIsVideoLoaded] = useState(false);
    const [publishDialogOpen, setPublishDialogOpen] = useState(false);
    const [showPublishButton, setShowPublishButton] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const videoContainerRef = useRef<HTMLDivElement>(null);
    const videoRef = useRef<HTMLVideoElement>(null);

    // 使用React Query获取队列信息
    const { isLoading: isLoadingQueueInfo, error: queueInfoError } = useQueueInfo(
        // 只有当任务处于队列中、等待中或处理中时才获取队列信息
        currentTask && ["queued", "processing", "pending"].includes(currentTask.status)
            ? currentTask.id
            : undefined
    );

    // 从Zustand存储中获取队列信息
    const queueInfo = useQueueInfoStore(
        (state) => currentTask ? state.getQueueInfo(currentTask.id) : null
    );

    // 处理全屏切换
    const toggleFullscreen = () => {
        if (!videoContainerRef.current) return;

        if (!document.fullscreenElement) {
            videoContainerRef.current.requestFullscreen().catch(err => {
                console.error(`全屏模式错误: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    };

    // 监听全屏状态变化
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
        };
    }, []);

    // 当视频任务变化时重置视频加载状态
    useEffect(() => {
        if (currentTask?.status === "completed" && currentTask.output_result?.video_url) {
            setIsVideoLoaded(false);

            // 当视频生成完成后，添加一个延迟显示发布按钮的效果
            setShowPublishButton(false);
            const timer = setTimeout(() => {
                setShowPublishButton(true);
            }, 1000);

            return () => clearTimeout(timer);
        }
    }, [currentTask?.id]);

    // 获取当前任务的宽高比
    const getTaskAspectRatio = (): AspectRatio => {
        // 如果有当前任务，优先使用任务的ratio参数
        if (currentTask?.input_params?.ratio) {
            return currentTask.input_params.ratio as AspectRatio;
        }

        // 否则使用组件接收的aspectRatio
        return aspectRatio;
    }

    // 为视频容器计算最佳尺寸类
    const getOptimalSizeClass = (ratio: AspectRatio) => {
        // 使用更加智能的响应式尺寸策略
        switch (ratio) {
            case "16:9":
                // 横屏视频：以宽度为主，设置宽高比和最大尺寸限制
                return "w-full max-w-[min(95vw,1600px)] max-h-[72vh] lg:max-h-[76vh] aspect-[16/9]"
            case "9:16":
                // 竖屏视频：设置固定高度比例，宽度通过宽高比自动计算
                return "h-[72vh] lg:h-[76vh] max-h-[72vh] lg:max-h-[76vh] aspect-[9/16]"
            case "1:1":
                // 正方形视频：取视口高度和宽度中较小的一个的80%作为边长
                return "aspect-square max-w-[min(72vh,95vw)] max-h-[min(72vh,95vw)] lg:max-w-[min(76vh,80vw)] lg:max-h-[min(76vh,80vw)]"
            default:
                return "w-full max-w-[min(95vw,1600px)] max-h-[72vh] lg:max-h-[76vh] aspect-[16/9]"
        }
    }

    // 处理发布按钮点击
    const handlePublishClick = () => {
        setPublishDialogOpen(true);
    };

    // 添加下载视频函数
    const handleDownloadVideo = async () => {
        if (!currentTask?.output_result?.video_url || isDownloading) return;
        
        setIsDownloading(true);
        
        try {
            // 获取prompt并截取前10个字符作为文件名
            const prompt = currentTask.input_params?.prompt || "video";
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const fileName = `Reelmind-${prompt.slice(0, 10)}-${timestamp}.mp4`;

            const success = await downloadVideo(currentTask.output_result.video_url, fileName);
            
            if (success) {
                console.log('Video download completed successfully');
            }
        } catch (error) {
            console.error('Video download failed:', error);
        } finally {
            setIsDownloading(false);
        }
    };

    // 格式化等待时间
    const formatWaitTime = (seconds?: number): string => {
        if (!seconds) return "Calculating...";

        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        if (minutes === 0) {
            return `${remainingSeconds} seconds`;
        } else if (minutes === 1) {
            return `1 minute ${remainingSeconds > 0 ? `${remainingSeconds} seconds` : ''}`;
        } else {
            return `${minutes} minutes ${remainingSeconds > 0 ? `${remainingSeconds} seconds` : ''}`;
        }
    };

    // 格式化预计开始/完成时间
    const formatEstimatedTime = (date?: Date): string => {
        if (!date) return "Calculating...";

        // 简单的自定义格式化函数，计算时间差并返回友好的格式
        const now = new Date();
        const targetDate = new Date(date);
        const diffMs = targetDate.getTime() - now.getTime();
        const diffMins = Math.round(diffMs / 60000);

        if (diffMins < 0) {
            return "just now";
        } else if (diffMins === 0) {
            return "in less than a minute";
        } else if (diffMins === 1) {
            return "in 1 minute";
        } else if (diffMins < 60) {
            return `in ${diffMins} minutes`;
        } else {
            const hours = Math.floor(diffMins / 60);
            if (hours === 1) {
                return "in 1 hour";
            } else if (hours < 24) {
                return `in ${hours} hours`;
            } else {
                const days = Math.floor(hours / 24);
                if (days === 1) {
                    return "in 1 day";
                } else {
                    return `in ${days} days`;
                }
            }
        }
    };

    // 渲染队列信息
    const renderQueueInfo = () => {
        if (!queueInfo) {
            if (isLoadingQueueInfo) {
                // 首次加载时显示加载中状态
                return (
                    <div className="fixed bottom-12 left-6 right-6 max-w-2xl mx-auto bg-black/80 backdrop-blur-md rounded-lg p-4 text-white border border-white/10 z-30">
                        <div className="flex flex-col space-y-3">
                            <div className="flex items-center justify-center">
                                <Clock className="h-4 w-4 mr-2 text-white animate-pulse" />
                                <p className="text-sm">Loading queue information...</p>
                            </div>
                        </div>
                    </div >
                );
            }
            return null;
        }

        // 判断是否在队列中或正在处理中
        const isInQueue = queueInfo.queue_position > 0;
        const isProcessing = queueInfo.is_processing === true;

        return (
            <div className="fixed bottom-12 left-6 right-6 max-w-3xl mx-auto bg-black/80 backdrop-blur-md rounded-lg p-4 text-white border border-white/10 z-30">
                <div className="flex flex-col space-y-3">
                    <div className="flex items-center justify-between">
                        <h3 className="text-base font-medium flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-white" />
                            {isProcessing ? "Processing" : "Queue Status"}
                        </h3>
                        {isInQueue && (
                            <span className="text-xs bg-white/10 text-white px-2 py-1 rounded-full">
                                Position: {queueInfo.queue_position} of {queueInfo.total_tasks_in_queue}
                            </span>
                        )}
                        {isProcessing && (
                            <span className="text-xs bg-white/10 text-green-300 px-2 py-1 rounded-full">
                                Processing
                            </span>
                        )}
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                        {isInQueue && (
                            <>
                                <div className="flex items-center">
                                    <Users className="h-3.5 w-3.5 mr-2 text-white/80" />
                                    <span className="text-white/80">Tasks in queue:</span>
                                    <span className="ml-auto font-medium">{queueInfo.total_tasks_in_queue}</span>
                                </div>

                                <div className="flex items-center">
                                    <Clock className="h-3.5 w-3.5 mr-2 text-white/80" />
                                    <span className="text-white/80">Estimated wait:</span>
                                    <span className="ml-auto font-medium">{formatWaitTime(queueInfo.estimated_wait_time_seconds)}</span>
                                </div>
                            </>
                        )}

                        {isProcessing && queueInfo.elapsed_time_seconds !== undefined && (
                            <div className="flex items-center">
                                <Clock className="h-3.5 w-3.5 mr-2 text-white/80" />
                                <span className="text-white/80">Elapsed time:</span>
                                <span className="ml-auto font-medium">{formatWaitTime(queueInfo.elapsed_time_seconds)}</span>
                            </div>
                        )}

                        {isProcessing && queueInfo.remaining_time_seconds !== undefined && queueInfo.remaining_time_seconds > 0 && (
                            <div className="flex items-center">
                                <Clock className="h-3.5 w-3.5 mr-2 text-white/80" />
                                <span className="text-white/80">Remaining time:</span>
                                <span className="ml-auto font-medium">{formatWaitTime(queueInfo.remaining_time_seconds)}</span>
                            </div>
                        )}

                        {queueInfo.estimated_start_time && (
                            <div className="flex items-center">
                                <Clock className="h-3.5 w-3.5 mr-2 text-white/80" />
                                <span className="text-white/80">Start time:</span>
                                <span className="ml-auto font-medium">{formatEstimatedTime(queueInfo.estimated_start_time)}</span>
                            </div>
                        )}

                        {queueInfo.estimated_completion_time && (
                            <div className="flex items-center">
                                <Clock className="h-3.5 w-3.5 mr-2 text-white/80" />
                                <span className="text-white/80">Completion time:</span>
                                <span className="ml-auto font-medium">{formatEstimatedTime(queueInfo.estimated_completion_time)}</span>
                            </div>
                        )}
                    </div>

                    {/* 进度条 */}
                    <div className="w-full bg-white/10 rounded-full h-1.5 mt-2">
                        {isInQueue && (
                            <div
                                className="bg-white h-1.5 rounded-full transition-all duration-500 ease-in-out"
                                style={{ width: `${Math.max(5, 100 - (queueInfo.queue_position / queueInfo.total_tasks_in_queue * 100))}%` }}
                            ></div>
                        )}
                        {isProcessing && queueInfo.elapsed_time_seconds !== undefined && queueInfo.remaining_time_seconds !== undefined && (
                            <div
                                className="bg-green-400 h-1.5 rounded-full transition-all duration-500 ease-in-out"
                                style={{
                                    width: `${Math.max(5, (queueInfo.elapsed_time_seconds / (queueInfo.elapsed_time_seconds + queueInfo.remaining_time_seconds) * 100))}%`
                                }}
                            ></div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    // 生成中的任务显示
    const renderGeneratingContent = () => {
        return (
            <div className="absolute inset-0 flex flex-col items-center justify-center">
                <div className="w-16 h-16 relative mb-4">
                    <div className="absolute inset-0 bg-primary/20 rounded-full animate-ping"></div>
                    <div className="relative w-full h-full flex items-center justify-center">
                        <Sparkles size={24} className="text-foreground animate-pulse" />
                    </div>
                </div>
                <div className="text-center space-y-1 px-4">
                    <p className="text-foreground">Generating your creative video...</p>
                </div>
            </div>
        );
    };

    // 完成的视频预览
    const renderCompletedVideo = () => {
        if (!currentTask?.output_result?.video_url) return null;

        return (
            <div className="absolute inset-0 flex items-center justify-center">
                <video
                    ref={videoRef}
                    src={currentTask.output_result.video_url}
                    className={`w-full h-full object-contain transition-opacity duration-300 ${isVideoLoaded ? 'opacity-100' : 'opacity-0'}`}
                    controls
                    autoPlay
                    loop
                    muted
                    onLoadedData={() => setIsVideoLoaded(true)}
                    controlsList="nodownload"
                    playsInline
                />

                {/* 控制按钮组 - 在移动端常态显示 */}
                <div className="absolute top-4 right-4 lg:top-7 lg:right-6 flex space-x-2 opacity-100 lg:opacity-0 lg:group-hover:opacity-100 transition-opacity z-10">
                    {/* 下载按钮 */}
                    <button
                        onClick={handleDownloadVideo}
                        disabled={isDownloading}
                        className="p-2 bg-primary/90 hover:bg-primary text-primary-foreground rounded-full transition-transform hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                        aria-label={isDownloading ? "downloading..." : "download video"}
                    >
                        {isDownloading ? (
                            <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                            <Download size={16} />
                        )}
                    </button>

                    {/* 发布按钮 */}
                    <button
                        onClick={handlePublishClick}
                        className="p-2 bg-primary/90 hover:bg-primary text-primary-foreground rounded-full transition-transform hover:scale-110"
                        aria-label="share to community"
                    >
                        <Share size={16} />
                    </button>

                    {/* 全屏按钮 */}
                    <button
                        onClick={toggleFullscreen}
                        className="p-2 bg-black/40 hover:bg-black/60 text-white rounded-full transition-transform hover:scale-110"
                        aria-label={isFullscreen ? "exit fullscreen" : "fullscreen"}
                    >
                        {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                    </button>
                </div>
            </div>
        );
    };

    // 默认空状态
    const renderEmptyState = () => {
        return (
            <div className="absolute inset-0 flex flex-col items-center justify-center">
                <div className="w-20 h-20 rounded-full bg-primary/20 flex items-center justify-center mb-4 backdrop-blur-xs border border-primary/20">
                    <Sparkles size={28} className="text-primary" />
                </div>
                <div className="text-center space-y-1 px-4">
                    <p className="text-foreground font-medium">Quickly generate your creative video</p>
                    <p className="text-sm text-muted-foreground">Adjust parameters and generate a high-quality video with one click</p>
                </div>
            </div>
        );
    };

    // 根据当前状态决定显示内容
    const renderPreviewContent = () => {
        if (isGenerating || (currentTask && ["pending", "queued", "processing"].includes(currentTask.status))) {
            return renderGeneratingContent();
        } else if (currentTask?.status === "completed" && currentTask.output_result) {
            return renderCompletedVideo();
        } else {
            return renderEmptyState();
        }
    };

    // 检查是否显示视频
    const isShowingVideo = currentTask?.status === "completed" && currentTask.output_result;

    // 确定使用哪个宽高比
    const effectiveAspectRatio = getTaskAspectRatio();

    return (
        <div className="h-full flex-1 flex flex-col items-center justify-center p-2 sm:p-4 lg:p-6 bg-background overflow-hidden relative">
            {/* 预览区域 - 使用flex-1确保它占据剩余空间 */}
            <div className="flex-1 flex items-center justify-center w-full h-full">
                <div
                    ref={videoContainerRef}
                    className={`
                        ${isShowingVideo ? getOptimalSizeClass(effectiveAspectRatio) : `w-full max-w-xl aspect-[${effectiveAspectRatio.replace(':', '/')}]`}
                        bg-card/90 backdrop-blur-xs rounded-lg
                        relative overflow-hidden group transition-all duration-150
                        ${isFullscreen ? 'fixed inset-0 z-50 max-w-none max-h-none w-full h-full rounded-none' : ''}
                    `}
                >
                    {/* 内容 */}
                    {renderPreviewContent()}

                    {/* 底部水印 */}
                    {isShowingVideo && <div className="absolute bottom-3 right-3 text-xs text-foreground opacity-80">
                        ReelMind AI
                    </div>}
                </div>
            </div>

            {/* 底部提示和操作区 - 在移动端隐藏详细信息，保留按钮 */}
            {currentTask?.status === "completed" && (
                <div className="w-full mt-2 lg:mt-4 flex flex-wrap items-center justify-between flex-shrink-0 gap-2">
                    <span className="text-xs lg:text-sm text-muted-foreground hidden sm:inline-block">Tips: More accurate descriptions will bring better results</span>
                    <div className="flex items-center gap-2 w-full sm:w-auto">
                        <span className="text-xs lg:text-sm text-muted-foreground hidden sm:inline-block">Quality: <span className="font-bold">{currentTask?.input_params?.definition === "720P" ? 'HD' : 'Standard'}</span></span>
                        <span className="text-xs lg:text-sm text-muted-foreground hidden sm:inline-block">Ratio: <span className="font-bold">{effectiveAspectRatio}</span></span>

                        <button
                            onClick={handleDownloadVideo}
                            disabled={isDownloading}
                            className={cn(
                                "px-3 lg:px-5 py-1.5 lg:py-2 bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded flex items-center w-full justify-center sm:w-auto sm:ml-3",
                                "transition-all duration-100 ease-in-out hover:ring-2 hover:ring-secondary/50",
                                "disabled:opacity-50 disabled:cursor-not-allowed",
                                showPublishButton ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
                            )}
                        >
                            {isDownloading ? (
                                <>
                                    <div className="w-3.5 h-3.5 border border-current border-t-transparent rounded-full animate-spin mr-1" />
                                    Downloading...
                                </>
                            ) : (
                                <>
                                    <Download size={14} className="mr-1" />
                                    Download
                                </>
                            )}
                        </button>

                        <button
                            onClick={handlePublishClick}
                            className={cn(
                                "px-3 lg:px-5 py-1.5 lg:py-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded flex items-center w-full justify-center sm:w-auto sm:ml-2",
                                "transition-all duration-100 ease-in-out hover:ring-2 hover:ring-primary/50",
                                showPublishButton ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
                            )}
                        >
                            <Share size={14} className="mr-1" />
                            Share to community
                        </button>
                    </div>
                </div>
            )}

            {/* 队列信息 - 移到主组件级别 */}
            {(currentTask?.status === "queued" || currentTask?.status === "pending" || currentTask?.status === "processing") && renderQueueInfo()}

            {/* 发布对话框 */}
            {currentTask && (
                <PublishDialog
                    open={publishDialogOpen}
                    onOpenChange={setPublishDialogOpen}
                    task={currentTask}
                />
            )}
        </div>
    )
}

