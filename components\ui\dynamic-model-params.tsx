'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, HelpCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { modelConfigApi, ModelInputConfig, ModelParameterConfig } from '@/lib/api/model-config';

interface DynamicModelParamsProps {
    modelId: string;
    values: Record<string, any>;
    onChange: (values: Record<string, any>) => void;
    errors?: Record<string, string>;
    className?: string;
}

interface ParameterFieldProps {
    paramName: string;
    paramConfig: ModelParameterConfig;
    value: any;
    onChange: (value: any) => void;
    error?: string;
}

/**
 * 单个参数字段组件
 */
const ParameterField: React.FC<ParameterFieldProps> = ({
    paramName,
    paramConfig,
    value,
    onChange,
    error
}) => {
    const label = modelConfigApi.getParameterLabel(paramName, paramConfig);
    const helpText = modelConfigApi.getParameterHelpText(paramConfig);

    const renderInput = () => {
        switch (paramConfig.type) {
            case 'boolean':
                return (
                    <div className="flex items-center space-x-2">
                        <Switch
                            checked={value || false}
                            onCheckedChange={onChange}
                        />
                        <span className="text-sm text-muted-foreground">
                            {value ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                );

            case 'number':
            case 'integer':
                if (paramConfig.minimum !== undefined && paramConfig.maximum !== undefined) {
                    // 使用滑块
                    return (
                        <div className="space-y-2">
                            <Slider
                                value={[value || paramConfig.default || paramConfig.minimum]}
                                onValueChange={(values) => onChange(values[0])}
                                min={paramConfig.minimum}
                                max={paramConfig.maximum}
                                step={paramConfig.type === 'integer' ? 1 : 0.1}
                                className="w-full"
                            />
                            <div className="flex justify-between text-xs text-muted-foreground">
                                <span>{paramConfig.minimum}</span>
                                <span className="font-medium">{value || paramConfig.default || paramConfig.minimum}</span>
                                <span>{paramConfig.maximum}</span>
                            </div>
                        </div>
                    );
                } else {
                    // 使用数字输入框
                    return (
                        <Input
                            type="number"
                            value={value || ''}
                            onChange={(e) => {
                                const val = e.target.value;
                                if (val === '') {
                                    onChange(undefined);
                                } else {
                                    const numVal = paramConfig.type === 'integer' 
                                        ? parseInt(val, 10) 
                                        : parseFloat(val);
                                    if (!isNaN(numVal)) {
                                        onChange(numVal);
                                    }
                                }
                            }}
                            placeholder={paramConfig.example?.toString() || paramConfig.default?.toString() || ''}
                            min={paramConfig.minimum}
                            max={paramConfig.maximum}
                            step={paramConfig.type === 'integer' ? 1 : 0.1}
                        />
                    );
                }

            case 'string':
                if (paramConfig.enum && paramConfig.enum.length > 0) {
                    // 使用选择框
                    return (
                        <Select value={value || ''} onValueChange={onChange}>
                            <SelectTrigger>
                                <SelectValue placeholder={`Select ${paramName}`} />
                            </SelectTrigger>
                            <SelectContent>
                                {paramConfig.enum.map((option) => (
                                    <SelectItem key={option.toString()} value={option.toString()}>
                                        {option.toString()}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    );
                } else if (paramName.toLowerCase().includes('prompt') || 
                          paramName.toLowerCase().includes('description') ||
                          (paramConfig.description && paramConfig.description.length > 100)) {
                    // 使用文本域
                    return (
                        <Textarea
                            value={value || ''}
                            onChange={(e) => onChange(e.target.value)}
                            placeholder={paramConfig.example || paramConfig.default || `Enter ${paramName}`}
                            rows={3}
                        />
                    );
                } else {
                    // 使用普通输入框
                    return (
                        <Input
                            type="text"
                            value={value || ''}
                            onChange={(e) => onChange(e.target.value)}
                            placeholder={paramConfig.example || paramConfig.default || `Enter ${paramName}`}
                        />
                    );
                }

            case 'array':
                return (
                    <Textarea
                        value={Array.isArray(value) ? value.join('\n') : ''}
                        onChange={(e) => {
                            const lines = e.target.value.split('\n').filter(line => line.trim());
                            onChange(lines);
                        }}
                        placeholder="Enter one item per line"
                        rows={3}
                    />
                );

            default:
                return (
                    <Input
                        type="text"
                        value={value || ''}
                        onChange={(e) => onChange(e.target.value)}
                        placeholder={paramConfig.example || paramConfig.default || `Enter ${paramName}`}
                    />
                );
        }
    };

    return (
        <div className="space-y-2">
            <div className="flex items-center space-x-2">
                <Label htmlFor={paramName} className="text-sm font-medium">
                    {label}
                </Label>
                {paramConfig.required && (
                    <Badge variant="secondary" className="text-xs">
                        Required
                    </Badge>
                )}
                {helpText && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent className="max-w-xs">
                                <p className="text-sm">{helpText}</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
            </div>
            
            {renderInput()}
            
            {error && (
                <div className="flex items-center space-x-1 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                </div>
            )}
        </div>
    );
};

/**
 * 动态模型参数组件
 */
export const DynamicModelParams: React.FC<DynamicModelParamsProps> = ({
    modelId,
    values,
    onChange,
    errors = {},
    className = ''
}) => {
    const [localValues, setLocalValues] = useState<Record<string, any>>(values);

    // 获取模型配置
    const { data: modelConfig, isLoading, error } = useQuery({
        queryKey: ['model-config', modelId],
        queryFn: () => modelConfigApi.getModelConfig(modelId),
        enabled: !!modelId,
        staleTime: 1000 * 60 * 10, // 10分钟
    });

    // 同步外部值变化
    useEffect(() => {
        setLocalValues(values);
    }, [values]);

    // 设置默认值
    useEffect(() => {
        if (modelConfig && Object.keys(localValues).length === 0) {
            const defaultValues: Record<string, any> = {};
            Object.entries(modelConfig.input).forEach(([paramName, paramConfig]) => {
                if (paramConfig.default !== undefined) {
                    defaultValues[paramName] = paramConfig.default;
                }
            });
            if (Object.keys(defaultValues).length > 0) {
                setLocalValues(defaultValues);
                onChange(defaultValues);
            }
        }
    }, [modelConfig, localValues, onChange]);

    const handleValueChange = (paramName: string, value: any) => {
        const newValues = { ...localValues, [paramName]: value };
        setLocalValues(newValues);
        onChange(newValues);
    };

    if (isLoading) {
        return (
            <Card className={className}>
                <CardContent className="p-6">
                    <div className="animate-pulse space-y-4">
                        <div className="h-4 bg-muted rounded w-1/4"></div>
                        <div className="h-10 bg-muted rounded"></div>
                        <div className="h-4 bg-muted rounded w-1/3"></div>
                        <div className="h-10 bg-muted rounded"></div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Alert className={className}>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                    Failed to load model configuration. Using basic parameters.
                </AlertDescription>
            </Alert>
        );
    }

    if (!modelConfig) {
        return (
            <Alert className={className}>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                    No advanced configuration available for this model.
                </AlertDescription>
            </Alert>
        );
    }

    // 按重要性和类型分组参数
    const requiredParams: [string, ModelParameterConfig][] = [];
    const optionalParams: [string, ModelParameterConfig][] = [];

    Object.entries(modelConfig.input).forEach(([paramName, paramConfig]) => {
        if (paramConfig.required) {
            requiredParams.push([paramName, paramConfig]);
        } else {
            optionalParams.push([paramName, paramConfig]);
        }
    });

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="text-lg">Model Parameters</CardTitle>
                <CardDescription>
                    Configure advanced parameters for {modelId}
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* 必填参数 */}
                {requiredParams.length > 0 && (
                    <div className="space-y-4">
                        <h4 className="text-sm font-semibold text-foreground">Required Parameters</h4>
                        <div className="grid gap-4">
                            {requiredParams.map(([paramName, paramConfig]) => (
                                <ParameterField
                                    key={paramName}
                                    paramName={paramName}
                                    paramConfig={paramConfig}
                                    value={localValues[paramName]}
                                    onChange={(value) => handleValueChange(paramName, value)}
                                    error={errors[paramName]}
                                />
                            ))}
                        </div>
                    </div>
                )}

                {/* 可选参数 */}
                {optionalParams.length > 0 && (
                    <div className="space-y-4">
                        <h4 className="text-sm font-semibold text-muted-foreground">Optional Parameters</h4>
                        <div className="grid gap-4">
                            {optionalParams.map(([paramName, paramConfig]) => (
                                <ParameterField
                                    key={paramName}
                                    paramName={paramName}
                                    paramConfig={paramConfig}
                                    value={localValues[paramName]}
                                    onChange={(value) => handleValueChange(paramName, value)}
                                    error={errors[paramName]}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
