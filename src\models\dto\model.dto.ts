import { IsString, <PERSON>NotEmpty, IsOptional, IsBoolean, IsObject, IsArray, ValidateNested, IsNumber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { VideoDefinition, VideoDuration, VideoRatio } from '../../common/constants/video';

export class ModelConfigDto {
    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(100)
    min_guidance_scale?: number;

    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(100)
    max_guidance_scale?: number;

    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(100)
    default_guidance_scale?: number;

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Max(100)
    min_steps?: number;

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Max(100)
    max_steps?: number;

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Max(100)
    default_steps?: number;

    @IsArray()
    @IsOptional()
    supported_definitions?: VideoDefinition[];

    @IsArray()
    @IsOptional()
    supported_durations?: VideoDuration[];

    @IsArray()
    @IsOptional()
    supported_ratios?: VideoRatio[];

    @IsBoolean()
    @IsOptional()
    allow_negative_prompt?: boolean;

    @IsBoolean()
    @IsOptional()
    allow_seed_input?: boolean;

    @IsBoolean()
    @IsOptional()
    allow_reference_image?: boolean;
}

export class CreateModelDto {
    @IsString()
    @IsNotEmpty()
    name: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsString()
    @IsNotEmpty()
    source: string;

    @IsString()
    @IsNotEmpty()
    model_type: string;

    @IsString()
    @IsNotEmpty()
    storage_path: string;

    @IsNumber()
    @IsOptional()
    nsfw_level?: number;

    @IsString()
    @IsOptional()
    size?: string;

    @IsString()
    @IsOptional()
    cover_img?: string;

    @IsString()
    @IsOptional()
    cover_video?: string;

    @IsNumber()
    @IsOptional()
    price?: number;

    @IsNumber()
    @IsOptional()
    weight?: number;

    @IsString()
    @IsOptional()
    category?: string;

    @IsString()
    @IsOptional()
    group?: string;

    @IsObject()
    @IsOptional()
    metadata?: any;

    @IsBoolean()
    @IsOptional()
    is_public?: boolean;

    @IsBoolean()
    @IsOptional()
    trainable?: boolean;

    @IsObject()
    @ValidateNested()
    @Type(() => ModelConfigDto)
    @IsOptional()
    default_config?: ModelConfigDto;

    @IsArray()
    @IsOptional()
    supported_features?: string[];

    // 仅供内部使用，接收来自控制器的用户ID
    @IsString()
    @IsOptional()
    user_id?: string;
}

export class UpdateModelDto {
    @IsString()
    @IsOptional()
    name?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsString()
    @IsOptional()
    source?: string;

    @IsString()
    @IsOptional()
    model_type?: string;

    @IsString()
    @IsOptional()
    storage_path?: string;

    @IsNumber()
    @IsOptional()
    nsfw_level?: number;

    @IsString()
    @IsOptional()
    size?: string;

    @IsString()
    @IsOptional()
    cover_img?: string;

    @IsString()
    @IsOptional()
    cover_video?: string;

    @IsNumber()
    @IsOptional()
    price?: number;

    @IsNumber()
    @IsOptional()
    weight?: number;

    @IsString()
    @IsOptional()
    category?: string;

    @IsString()
    @IsOptional()
    group?: string;

    @IsObject()
    @IsOptional()
    metadata?: any;

    @IsBoolean()
    @IsOptional()
    is_public?: boolean;

    @IsObject()
    @ValidateNested()
    @Type(() => ModelConfigDto)
    @IsOptional()
    default_config?: ModelConfigDto;

    @IsArray()
    @IsOptional()
    supported_features?: string[];
}

export class UpdateModelVisibilityDto {
    @IsBoolean()
    @IsNotEmpty()
    is_public: boolean;
}

export class ModelDto {
    id: string;
    name: string;
    description?: string;
    is_public: boolean;
    created_at: Date;
    updated_at: Date;
    source: string;
    model_type: string;
    storage_path: string;
    nsfw_level: number;
    size?: string;
    cover_img?: string;
    default_config: ModelConfigDto;
    supported_features: string[];
    thumbnail_url?: string;
    usage_count?: number;
}

export class ModelListResponseDto {
    models: ModelDto[];
    total: number;
} 