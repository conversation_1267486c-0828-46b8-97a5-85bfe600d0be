export enum GenerationType {
    TextToVideo = "text-to-video",
    ImageToVideo = "image-to-video",
    VideoToVideo = "video-to-video"
}
export type QualityMode = "standard" | "high"
export type Duration = "5" | "8" | "10"
export type AspectRatio = "16:9" | "9:16" | "1:1"

// API 相关类型
export type APIGenType = "text-to-video" | "image-to-video" | "video-to-video"
export type APIDefinition = "480P" | "720P"
export type APIDuration = "5" | "8" | "10"
export type APIRatio = "9:16" | "16:9" | "1:1"
export type APIStatus = "pending" | "queued" | "processing" | "completed" | "failed"

export interface VideoGenerationParams {
    type: GenerationType
    prompt?: string
    images?: string[]
    creativityLevel: number
    quality: QualityMode
    duration: Duration
    aspectRatio: AspectRatio
}

export interface GeneratedVideo {
    id: string
    url: string
    thumbnail: string
    title?: string
    duration?: string
    createdAt: string | Date
    params: VideoGenerationParams
}

export const mapUIToAPIDefinition = (uiQuality: QualityMode): APIDefinition => {
    return uiQuality === "standard" ? "480P" : "720P";
}

export const mapUIToAPIDuration = (uiDuration: Duration): APIDuration => {
    return `${uiDuration}s` as APIDuration;
}

