import { apiClient } from './client';
import { API_CONFIG } from './config';

/**
 * 模型参数配置类型定义
 */
export interface ModelParameterConfig {
    type: string;
    required: boolean;
    description?: string;
    default?: any;
    enum?: string[] | number[];
    example?: any;
    minimum?: number;
    maximum?: number;
    format?: string;
}

export interface ModelInputConfig {
    [paramName: string]: ModelParameterConfig;
}

export interface ModelOutputConfig {
    [paramName: string]: ModelParameterConfig;
}

export interface ModelConfig {
    category: string;
    input: ModelInputConfig;
    output: ModelOutputConfig;
}

export interface SupportedModels {
    modelIds: string[];
    categories: string[];
    lastUpdated: string;
}

export interface CategoryModels {
    category: string;
    modelIds: string[];
}

export interface ModelParamsValidationResult {
    isValid: boolean;
    errors: string[];
    validatedParams: Record<string, any>;
}

export interface ValidateModelParamsRequest {
    modelId: string;
    params: Record<string, any>;
}

/**
 * 模型配置API服务
 */
export const modelConfigApi = {
    /**
     * 获取支持的模型配置列表
     */
    getSupportedModels: async (): Promise<SupportedModels> => {
        const response = await apiClient.get<{ success: boolean; data: SupportedModels }>(
            `${API_CONFIG.ENDPOINTS.MODEL.GET_MODELS}/config/supported`
        );
        return response.data;
    },

    /**
     * 获取特定模型的参数配置
     * @param modelId 模型ID
     */
    getModelConfig: async (modelId: string): Promise<ModelConfig> => {
        const response = await apiClient.get<{ success: boolean; data: ModelConfig }>(
            `${API_CONFIG.ENDPOINTS.MODEL.GET_MODELS}/config/${encodeURIComponent(modelId)}`
        );
        return response.data;
    },

    /**
     * 根据分类获取模型列表
     * @param category 模型分类
     */
    getModelsByCategory: async (category: string): Promise<CategoryModels> => {
        const response = await apiClient.get<{ success: boolean; data: CategoryModels }>(
            `${API_CONFIG.ENDPOINTS.MODEL.GET_MODELS}/config/category/${encodeURIComponent(category)}`
        );
        return response.data;
    },

    /**
     * 验证模型参数
     * @param request 验证请求
     */
    validateModelParams: async (request: ValidateModelParamsRequest): Promise<ModelParamsValidationResult> => {
        const response = await apiClient.post<{ success: boolean; data: ModelParamsValidationResult }>(
            `${API_CONFIG.ENDPOINTS.MODEL.GET_MODELS}/config/validate`,
            request
        );
        return response.data;
    },

    /**
     * 检查模型是否支持配置
     * @param modelId 模型ID
     */
    hasModelConfig: async (modelId: string): Promise<boolean> => {
        try {
            await modelConfigApi.getModelConfig(modelId);
            return true;
        } catch (error) {
            return false;
        }
    },

    /**
     * 获取模型的输入参数配置
     * @param modelId 模型ID
     */
    getModelInputConfig: async (modelId: string): Promise<ModelInputConfig> => {
        const config = await modelConfigApi.getModelConfig(modelId);
        return config.input;
    },

    /**
     * 获取模型的输出参数配置
     * @param modelId 模型ID
     */
    getModelOutputConfig: async (modelId: string): Promise<ModelOutputConfig> => {
        const config = await modelConfigApi.getModelConfig(modelId);
        return config.output;
    },

    /**
     * 为模型参数设置默认值
     * @param modelId 模型ID
     * @param params 当前参数
     */
    setDefaultParams: async (modelId: string, params: Record<string, any> = {}): Promise<Record<string, any>> => {
        try {
            const inputConfig = await modelConfigApi.getModelInputConfig(modelId);
            const defaultParams = { ...params };

            // 为每个参数设置默认值
            Object.entries(inputConfig).forEach(([paramName, paramConfig]) => {
                if (defaultParams[paramName] === undefined && paramConfig.default !== undefined) {
                    defaultParams[paramName] = paramConfig.default;
                }
            });

            return defaultParams;
        } catch (error) {
            console.warn(`Failed to set default params for model ${modelId}:`, error);
            return params;
        }
    },

    /**
     * 获取参数的显示标签
     * @param paramName 参数名
     * @param paramConfig 参数配置
     */
    getParameterLabel: (paramName: string, paramConfig: ModelParameterConfig): string => {
        // 将参数名转换为更友好的显示名称
        const label = paramName
            .replace(/_/g, ' ')
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();

        return paramConfig.required ? `${label} *` : label;
    },

    /**
     * 获取参数的帮助文本
     * @param paramConfig 参数配置
     */
    getParameterHelpText: (paramConfig: ModelParameterConfig): string => {
        const parts: string[] = [];

        if (paramConfig.description) {
            parts.push(paramConfig.description);
        }

        if (paramConfig.type) {
            parts.push(`Type: ${paramConfig.type}`);
        }

        if (paramConfig.minimum !== undefined || paramConfig.maximum !== undefined) {
            if (paramConfig.minimum !== undefined && paramConfig.maximum !== undefined) {
                parts.push(`Range: ${paramConfig.minimum} - ${paramConfig.maximum}`);
            } else if (paramConfig.minimum !== undefined) {
                parts.push(`Minimum: ${paramConfig.minimum}`);
            } else if (paramConfig.maximum !== undefined) {
                parts.push(`Maximum: ${paramConfig.maximum}`);
            }
        }

        if (paramConfig.enum && paramConfig.enum.length > 0) {
            parts.push(`Options: ${paramConfig.enum.join(', ')}`);
        }

        if (paramConfig.default !== undefined) {
            parts.push(`Default: ${paramConfig.default}`);
        }

        if (paramConfig.example !== undefined) {
            parts.push(`Example: ${paramConfig.example}`);
        }

        return parts.join(' | ');
    },

    /**
     * 验证单个参数值
     * @param value 参数值
     * @param paramConfig 参数配置
     */
    validateParameterValue: (value: any, paramConfig: ModelParameterConfig): string | null => {
        // 检查必填参数
        if (paramConfig.required && (value === undefined || value === null || value === '')) {
            return 'This field is required';
        }

        // 如果值为空且不是必填，则通过验证
        if (value === undefined || value === null || value === '') {
            return null;
        }

        // 类型验证
        switch (paramConfig.type) {
            case 'string':
                if (typeof value !== 'string') {
                    return 'Must be a string';
                }
                break;
            case 'number':
                if (typeof value !== 'number' || isNaN(value)) {
                    return 'Must be a valid number';
                }
                break;
            case 'integer':
                if (typeof value !== 'number' || !Number.isInteger(value)) {
                    return 'Must be an integer';
                }
                break;
            case 'boolean':
                if (typeof value !== 'boolean') {
                    return 'Must be true or false';
                }
                break;
            case 'array':
                if (!Array.isArray(value)) {
                    return 'Must be an array';
                }
                break;
            case 'object':
                if (typeof value !== 'object' || value === null || Array.isArray(value)) {
                    return 'Must be an object';
                }
                break;
        }

        // 枚举值验证
        if (paramConfig.enum && !paramConfig.enum.includes(value)) {
            return `Must be one of: ${paramConfig.enum.join(', ')}`;
        }

        // 数值范围验证
        if ((paramConfig.type === 'number' || paramConfig.type === 'integer') && typeof value === 'number') {
            if (paramConfig.minimum !== undefined && value < paramConfig.minimum) {
                return `Must be >= ${paramConfig.minimum}`;
            }
            if (paramConfig.maximum !== undefined && value > paramConfig.maximum) {
                return `Must be <= ${paramConfig.maximum}`;
            }
        }

        return null;
    }
};
