'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { modelConfigApi, ModelConfig, SupportedModels, CategoryModels, ModelParamsValidationResult } from '@/lib/api/model-config';

/**
 * 获取支持的模型配置列表
 */
export const useSupportedModels = () => {
    return useQuery<SupportedModels>({
        queryKey: ['supported-models'],
        queryFn: modelConfigApi.getSupportedModels,
        staleTime: 1000 * 60 * 30, // 30分钟
        gcTime: 1000 * 60 * 60, // 1小时
    });
};

/**
 * 获取特定模型的参数配置
 */
export const useModelConfig = (modelId: string | null) => {
    return useQuery<ModelConfig>({
        queryKey: ['model-config', modelId],
        queryFn: () => modelConfigApi.getModelConfig(modelId!),
        enabled: !!modelId,
        staleTime: 1000 * 60 * 10, // 10分钟
        gcTime: 1000 * 60 * 30, // 30分钟
        retry: (failureCount, error: any) => {
            // 如果是404错误（模型配置不存在），不重试
            if (error?.response?.status === 404) {
                return false;
            }
            return failureCount < 3;
        },
    });
};

/**
 * 根据分类获取模型列表
 */
export const useModelsByCategory = (category: string | null) => {
    return useQuery<CategoryModels>({
        queryKey: ['models-by-category', category],
        queryFn: () => modelConfigApi.getModelsByCategory(category!),
        enabled: !!category,
        staleTime: 1000 * 60 * 15, // 15分钟
        gcTime: 1000 * 60 * 60, // 1小时
    });
};

/**
 * 验证模型参数
 */
export const useValidateModelParams = () => {
    return useMutation<
        ModelParamsValidationResult,
        Error,
        { modelId: string; params: Record<string, any> }
    >({
        mutationFn: ({ modelId, params }) => 
            modelConfigApi.validateModelParams({ modelId, params }),
        onError: (error) => {
            console.error('Parameter validation failed:', error);
        },
    });
};

/**
 * 检查模型是否支持高级配置
 */
export const useHasModelConfig = (modelId: string | null) => {
    return useQuery<boolean>({
        queryKey: ['has-model-config', modelId],
        queryFn: () => modelConfigApi.hasModelConfig(modelId!),
        enabled: !!modelId,
        staleTime: 1000 * 60 * 10, // 10分钟
        gcTime: 1000 * 60 * 30, // 30分钟
    });
};

/**
 * 获取模型的输入参数配置
 */
export const useModelInputConfig = (modelId: string | null) => {
    return useQuery({
        queryKey: ['model-input-config', modelId],
        queryFn: () => modelConfigApi.getModelInputConfig(modelId!),
        enabled: !!modelId,
        staleTime: 1000 * 60 * 10, // 10分钟
        gcTime: 1000 * 60 * 30, // 30分钟
        retry: (failureCount, error: any) => {
            if (error?.response?.status === 404) {
                return false;
            }
            return failureCount < 3;
        },
    });
};

/**
 * 为模型参数设置默认值的hook
 */
export const useModelDefaultParams = (modelId: string | null, initialParams: Record<string, any> = {}) => {
    return useQuery({
        queryKey: ['model-default-params', modelId, initialParams],
        queryFn: () => modelConfigApi.setDefaultParams(modelId!, initialParams),
        enabled: !!modelId,
        staleTime: 1000 * 60 * 5, // 5分钟
        gcTime: 1000 * 60 * 15, // 15分钟
    });
};

/**
 * 批量获取多个模型的配置
 */
export const useMultipleModelConfigs = (modelIds: string[]) => {
    const queryClient = useQueryClient();

    return useQuery({
        queryKey: ['multiple-model-configs', modelIds],
        queryFn: async () => {
            const configs: Record<string, ModelConfig | null> = {};
            
            await Promise.allSettled(
                modelIds.map(async (modelId) => {
                    try {
                        const config = await queryClient.fetchQuery({
                            queryKey: ['model-config', modelId],
                            queryFn: () => modelConfigApi.getModelConfig(modelId),
                            staleTime: 1000 * 60 * 10,
                        });
                        configs[modelId] = config;
                    } catch (error) {
                        configs[modelId] = null;
                    }
                })
            );

            return configs;
        },
        enabled: modelIds.length > 0,
        staleTime: 1000 * 60 * 10, // 10分钟
        gcTime: 1000 * 60 * 30, // 30分钟
    });
};

/**
 * 实时参数验证hook
 */
export const useRealtimeParamValidation = (
    modelId: string | null,
    params: Record<string, any>,
    debounceMs: number = 500
) => {
    const validateMutation = useValidateModelParams();
    
    return useQuery({
        queryKey: ['realtime-validation', modelId, params],
        queryFn: () => {
            if (!modelId) return null;
            return modelConfigApi.validateModelParams({ modelId, params });
        },
        enabled: !!modelId && Object.keys(params).length > 0,
        staleTime: 0, // 总是重新验证
        gcTime: 1000 * 60, // 1分钟
        refetchOnWindowFocus: false,
        retry: false,
    });
};

/**
 * 模型配置缓存管理
 */
export const useModelConfigCache = () => {
    const queryClient = useQueryClient();

    const prefetchModelConfig = async (modelId: string) => {
        await queryClient.prefetchQuery({
            queryKey: ['model-config', modelId],
            queryFn: () => modelConfigApi.getModelConfig(modelId),
            staleTime: 1000 * 60 * 10,
        });
    };

    const invalidateModelConfig = (modelId?: string) => {
        if (modelId) {
            queryClient.invalidateQueries({ queryKey: ['model-config', modelId] });
        } else {
            queryClient.invalidateQueries({ queryKey: ['model-config'] });
        }
    };

    const clearModelConfigCache = () => {
        queryClient.removeQueries({ queryKey: ['model-config'] });
    };

    const refreshSupportedModels = () => {
        queryClient.invalidateQueries({ queryKey: ['supported-models'] });
    };

    return {
        prefetchModelConfig,
        invalidateModelConfig,
        clearModelConfigCache,
        refreshSupportedModels,
    };
};
