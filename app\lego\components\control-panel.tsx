"use client";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Sliders, Image, Cpu } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { useLegoStore } from "../store"
import { useGenerateImage, useLegoModels, useFluxUsageLimit } from "../hooks"
import { AspectRatio } from "../types"
import { Responsive } from "@/components/ui/device-view"
import { useEffect, useState, useRef } from "react"
import { FluxImageUploader, FluxImageUploaderRef } from "./flux-image-uploader"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"

export function ControlPanel() {
    const {
        userPrompt,
        setUserPrompt,
        negativePrompt,
        setNegativePrompt,
        seed,
        setSeed,
        steps,
        setSteps,
        guidanceScale,
        setGuidanceScale,
        aspectRatio,
        setAs<PERSON>Ratio,
        isGenerating,
        setIsGenerating,
        error,
        isSidebarCollapsed,
        setIsSidebarCollapsed,
        selectedModel,
        setSelectedModel,
        availableModels,
        isLoadingModels,
        fluxImages
    } = useLegoStore();

    // Popover开关状态
    const [isModelPopoverOpen, setIsModelPopoverOpen] = useState(false);

    // FluxImageUploader的ref
    const fluxImageUploaderRef = useRef<FluxImageUploaderRef>(null);

    // 获取用户认证状态和路由
    const { user } = useAuth();
    const router = useRouter();

    // 获取模型列表
    useLegoModels();

    // 获取FLUX使用限制信息
    const { data: fluxUsageLimit, refetch: refetchFluxUsage } = useFluxUsageLimit();

    // 设置默认模型（UNO）- 只在没有通过URL参数选择模型时执行
    useEffect(() => {
        if (!selectedModel && availableModels.length > 0) {
            // 延迟一点时间，让PromptInitializer有机会处理URL参数
            const timer = setTimeout(() => {
                // 再次检查是否已经有选中的模型（可能通过URL参数设置）
                const currentState = useLegoStore.getState();
                if (!currentState.selectedModel) {
                    const defaultModel = availableModels.find(model =>
                        model.name.toLowerCase().includes('uno')
                    ) || availableModels[0];
                    setSelectedModel(defaultModel);
                }
            }, 100);

            return () => clearTimeout(timer);
        }
    }, [selectedModel, availableModels, setSelectedModel]);

    // 处理模型选择
    const handleModelSelect = (model: any) => {
        setSelectedModel(model);
        setIsModelPopoverOpen(false); // 选择后关闭Popover

        // 检查新模型的图片限制，如果当前图片数量超过限制，需要移除多余的图片
        const newImageLimit = model.image_limit || 2;
        if (fluxImages.length > newImageLimit) {
            // 从 store 中获取移除图片的方法
            const { removeFluxImage } = useLegoStore.getState();
            // 移除超出限制的图片（从后往前移除）
            for (let i = fluxImages.length - 1; i >= newImageLimit; i--) {
                removeFluxImage(i);
            }
        }
    };

    const { mutate: generateImage } = useGenerateImage();

    // 检查是否为FLUX模型
    const isFluxModel = selectedModel?.name.toLowerCase().includes('flux') || false;

    // 检查FLUX模型使用限制
    const isFluxLimitReached = isFluxModel && fluxUsageLimit && !fluxUsageLimit.canUse;

    const isDisabled = isGenerating || !userPrompt.trim() || !selectedModel || isFluxLimitReached;

    const handleGenerate = () => {
        if (!userPrompt.trim()) return;

        // 检查FLUX模型使用限制
        if (isFluxModel && fluxUsageLimit && !fluxUsageLimit.canUse) {
            return; // 如果达到限制，直接返回
        }

        // 确保界面立即响应，即使API调用还在进行中
        generateImage(undefined, {
            onSuccess: (data) => {
                // API调用成功完成时的额外逻辑
                if (data.response && data.response.success) {
                    // 检查是否返回了taskId
                    if (!data.taskId) {
                        // 如果没有taskId，确保重置生成状态
                        setIsGenerating(false);
                    }

                    // 如果是FLUX模型，立即刷新使用限制信息
                    if (isFluxModel) {
                        refetchFluxUsage();
                    }

                    // 滚动到顶部，让用户看到新生成的图片
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    // 如果响应不成功，确保重置生成状态
                    setIsGenerating(false);
                }
            },
            onError: (error) => {
                console.error('Failed to generate image:', error);
                // 确保在错误时重置生成状态
                setIsGenerating(false);
            }
        });
    };

    const generateRandomSeed = () => {
        setSeed(Math.floor(Math.random() * 1000000).toString());
    };

    // 处理侧边栏折叠/展开或图片上传
    const handleToggleSidebar = () => {
        if (isFluxModel) {
            // FLUX模型时直接触发文件选择
            fluxImageUploaderRef.current?.triggerFileInput();
        } else {
            // 非FLUX模型时切换侧边栏
            setIsSidebarCollapsed(!isSidebarCollapsed);
        }
    };

    // 创建移动设备的控制面板内容
    const mobileControlPanel = (
        <div className="p-2 z-10 bg-background backdrop-blur-sm shadow-2xl rounded-xl">
            {/* Mobile controls - all in a single row */}
            <div className="grid grid-cols-5 gap-1 mb-1">
                {/* Mobile Add Image button */}
                <button
                    onClick={handleToggleSidebar}
                    className="text-xs text-foreground/70 hover:text-foreground flex items-center justify-center bg-secondary hover:bg-secondary/80 py-1.5 px-1 rounded"
                    title={isSidebarCollapsed ? "Show sidebar" : "Hide sidebar"}
                >
                    <Image size={12} className="mr-0.5" />
                    <span className="truncate">
                        {isFluxModel ? `Add (${fluxImages.length}/${selectedModel?.image_limit || 2})` :
                            isSidebarCollapsed ? "Add" : "Hide"}
                    </span>
                </button>

                {/* Mobile Model selection */}
                <Popover open={isModelPopoverOpen} onOpenChange={setIsModelPopoverOpen}>
                    <PopoverTrigger asChild>
                        <button className="text-xs text-foreground/70 hover:text-foreground flex items-center justify-center bg-secondary hover:bg-secondary/80 py-1.5 px-1 rounded">
                            <Cpu size={12} className="mr-0.5" />
                            <span className="truncate">{selectedModel?.name.substring(0, 3) || 'Mod'}</span>
                        </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-0 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl rounded-xl" align="start">
                        <div className="">
                            {isLoadingModels ? (
                                <div className="p-6 text-sm text-gray-500 text-center">Loading models...</div>
                            ) : availableModels.length === 0 ? (
                                <div className="p-6 text-sm text-gray-500 text-center">No models available</div>
                            ) : (
                                <div className="p-3 space-y-2">
                                    {availableModels.map((model) => (
                                        <button
                                            key={model.name}
                                            onClick={() => handleModelSelect(model)}
                                            className={cn(
                                                "w-full p-4 rounded-lg text-left transition-all duration-200 group relative border",
                                                selectedModel?.name === model.name
                                                    ? "bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-600"
                                                    : "hover:bg-gray-50 dark:hover:bg-gray-800/50 border-transparent"
                                            )}
                                        >
                                            <div className="flex items-start justify-between">
                                                <div className="flex items-start space-x-3 flex-1 min-w-0">
                                                    <div className="flex-shrink-0 mt-1">
                                                        <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                            <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                                                                {model.name.charAt(0).toUpperCase()}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="flex-1 min-w-0">
                                                        <div className="font-semibold text-gray-900 dark:text-white text-sm truncate">
                                                            {model.name}
                                                        </div>
                                                        {model.description && (
                                                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                                                                {model.description}
                                                            </div>
                                                        )}
                                                        <div className="flex items-center space-x-2 mt-2">
                                                            {model.price === 0 ? (
                                                                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                                    Free
                                                                </span>
                                                            ) : (
                                                                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                                                                    {model.price} credits
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                                {selectedModel?.name === model.name && (
                                                    <div className="flex-shrink-0 ml-3">
                                                        <div className="w-5 h-5 rounded-full bg-green-600 flex items-center justify-center">
                                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                    </PopoverContent>
                </Popover>

                {/* Mobile Seed control */}
                <Popover>
                    <PopoverTrigger asChild>
                        <button className="text-xs text-foreground/70 hover:text-foreground flex items-center justify-center bg-secondary hover:bg-secondary/80 py-1.5 px-1 rounded">
                            <Dices size={12} className="mr-0.5" />
                            <span className="truncate">S:{seed.length > 3 ? seed.substring(0, 3) : seed}</span>
                        </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-56 p-3 bg-popover border-border text-popover-foreground" align="start">
                        <Label className="block text-xs font-medium mb-2">Random Seed</Label>
                        <div className="flex gap-1">
                            <input
                                type="text"
                                placeholder="Random seed"
                                value={seed}
                                onChange={(e) => setSeed(e.target.value)}
                                className="flex-1 px-2 py-1.5 text-xs rounded bg-input border-input focus:border-ring focus:ring-0"
                                aria-label="Seed value"
                                title="Enter a seed value"
                            />
                            <button
                                onClick={generateRandomSeed}
                                className="bg-secondary hover:bg-secondary/80 px-2 py-1.5 rounded transition-colors"
                                title="Generate random seed"
                                aria-label="Generate random seed"
                            >
                                <Dices size={14} />
                            </button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">Same seed will produce similar results</p>
                    </PopoverContent>
                </Popover>

                {/* Mobile Ratio control */}
                <Popover>
                    <PopoverTrigger asChild>
                        <button className="text-xs text-foreground/70 hover:text-foreground flex items-center justify-center bg-secondary hover:bg-secondary/80 py-1.5 px-1 rounded">
                            <Ratio size={12} className="mr-0.5" />
                            <span className="truncate">{aspectRatio}</span>
                        </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48 p-2 bg-popover border-border text-popover-foreground" align="start">
                        <h4 className="text-xs font-medium mb-2">Aspect Ratio</h4>
                        <div className="grid grid-cols-3 gap-1">
                            <button
                                onClick={() => setAspectRatio("16:9" as AspectRatio)}
                                className={cn(
                                    "p-1.5 rounded text-xs flex flex-col items-center justify-center",
                                    aspectRatio === "16:9"
                                        ? "bg-accent text-accent-foreground"
                                        : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                )}
                            >
                                <div className="w-6 h-3.5 bg-foreground/20 mb-1 rounded"></div>
                                16:9
                            </button>
                            <button
                                onClick={() => setAspectRatio("9:16" as AspectRatio)}
                                className={cn(
                                    "p-1.5 rounded text-xs flex flex-col items-center justify-center",
                                    aspectRatio === "9:16"
                                        ? "bg-accent text-accent-foreground"
                                        : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                )}
                            >
                                <div className="w-3.5 h-6 bg-foreground/20 mb-1 rounded"></div>
                                9:16
                            </button>
                            <button
                                onClick={() => setAspectRatio("1:1" as AspectRatio)}
                                className={cn(
                                    "p-1.5 rounded text-xs flex flex-col items-center justify-center",
                                    aspectRatio === "1:1"
                                        ? "bg-accent text-accent-foreground"
                                        : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                )}
                            >
                                <div className="w-4 h-4 bg-foreground/20 mb-1 rounded"></div>
                                1:1
                            </button>
                        </div>
                    </PopoverContent>
                </Popover>

                {/* Mobile Advanced control */}
                <Popover>
                    <PopoverTrigger asChild>
                        <button
                            className="text-xs text-foreground/70 hover:text-foreground flex items-center justify-center bg-secondary hover:bg-secondary/80 py-1.5 px-1 rounded"
                            aria-label="Advanced settings"
                            title="Advanced settings"
                        >
                            <Sliders size={12} className="mr-0.5" />
                            <span className="truncate">Adv</span>
                        </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-72 p-3 bg-popover border-border text-popover-foreground" align="end">
                        {/* Mobile advanced settings */}
                        <div className="mb-4">
                            <Label className="block text-xs font-medium mb-2">Negative Prompt</Label>
                            <textarea
                                placeholder="Elements you want to exclude..."
                                value={negativePrompt}
                                onChange={(e) => setNegativePrompt(e.target.value)}
                                className="w-full h-20 px-3 py-2 text-xs rounded-lg bg-input border-input focus:outline-none focus:ring-0 placeholder:text-muted-foreground text-foreground resize-none"
                                aria-label="Negative prompt"
                                title="Specify elements to exclude"
                            />
                        </div>

                        {/* Mobile steps and guidance scale */}
                        <div className="grid grid-cols-1 gap-4">
                            <div>
                                <div className="flex justify-between mb-2">
                                    <Label className="text-xs font-medium">Steps: {steps}</Label>
                                </div>
                                <input
                                    type="range"
                                    min="20"
                                    max="50"
                                    value={steps}
                                    onChange={(e) => setSteps(parseInt(e.target.value))}
                                    className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                                    aria-label={`Steps control, current value: ${steps}`}
                                    title="Adjust steps for generation"
                                />
                            </div>

                            <div>
                                <div className="flex justify-between mb-2">
                                    <Label className="text-xs font-medium">Guidance Scale: {guidanceScale}</Label>
                                </div>
                                <input
                                    type="range"
                                    min="1"
                                    max="10"
                                    step="0.5"
                                    value={guidanceScale}
                                    onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                                    className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                                    aria-label={`Guidance scale control, current value: ${guidanceScale}`}
                                    title="Adjust guidance scale for generation"
                                />
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            </div>

            {/* FLUX模型图片上传区域 - 移动端 */}
            {isFluxModel && (
                <div className="mb-3">
                    <FluxImageUploader ref={fluxImageUploaderRef} maxImages={selectedModel?.image_limit || 2} />
                </div>
            )}

            {/* Input field with Generate button on right side */}
            <div className="flex mb-1">
                {/* Text input area */}
                <div className="flex-1 relative">
                    <textarea
                        placeholder="Create your keyframe"
                        maxLength={2000}
                        value={userPrompt}
                        onChange={(e) => setUserPrompt(e.target.value)}
                        className="w-full h-[60px] px-3 py-2 text-sm rounded-lg bg-input border-none focus:outline-none focus:ring-0 placeholder:text-muted-foreground text-foreground resize-none pr-[76px]"
                    />

                    {/* Generate button for mobile - positioned in the middle right of textarea */}
                    <div className="absolute top-1/2 right-2 transform -translate-y-1/2">
                        <button
                            onClick={handleGenerate}
                            disabled={isDisabled}
                            className={`px-3 py-2.5 rounded-lg text-white font-medium flex items-center h-[40px] ${isDisabled ? 'bg-gray-400 cursor-not-allowed' : isGenerating ? 'bg-primary/50' : 'bg-primary hover:bg-primary/80'}`}
                            title={isFluxLimitReached ? `FLUX models limited to ${fluxUsageLimit?.limit || 2} uses for non-members` : undefined}
                        >
                            {isGenerating ? (
                                <>
                                    <div className="w-3.5 h-3.5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-1"></div>
                                    <span className="text-xs">Generating...</span>
                                </>
                            ) : isFluxLimitReached ? (
                                <>
                                    <span className="text-xs">Limit Reached</span>
                                </>
                            ) : (
                                <>
                                    <Sparkles size={12} className="mr-1" />
                                    <span className="text-xs">Generate</span>
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* 错误信息和FLUX限制提示 */}
            {error && (
                <div className="mt-1 px-3 py-2 bg-destructive/10 text-destructive text-sm rounded border border-destructive/20">
                    {error}
                </div>
            )}
            {isFluxLimitReached && (
                <div className="mt-1 px-3 py-2 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 text-sm rounded border border-orange-200 dark:border-orange-800">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            Free users can only use FLUX models {fluxUsageLimit?.limit || 2} times. You have used {fluxUsageLimit?.usageCount || 0}/{fluxUsageLimit?.limit || 2}.
                        </div>
                        <button
                            onClick={() => router.push('/membership')}
                            className="ml-2 px-2 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs font-medium rounded transition-colors whitespace-nowrap"
                        >
                            Upgrade
                        </button>
                    </div>
                </div>
            )}
        </div>
    );

    // 创建平板设备的控制面板内容
    const tabletControlPanel = mobileControlPanel;

    // 创建桌面设备的控制面板内容
    const desktopControlPanel = (
        <div className="fixed bottom-5 left-0 right-0 container w-xl xl:w-3xl 2xl:w-6xl mx-auto p-4 z-10 bg-background backdrop-blur-sm shadow-2xl rounded-xl">
            {/* 输入字段与生成按钮 */}
            <div className="flex mb-4 gap-4">
                {/* FLUX模型图片上传区域 - 左侧 */}
                {isFluxModel && (
                    <div className="flex-shrink-0">
                        <FluxImageUploader ref={fluxImageUploaderRef} maxImages={selectedModel?.image_limit} />
                    </div>
                )}

                {/* 文本输入区域 */}
                <div className="flex-1 relative">
                    <textarea
                        placeholder="Create your keyframe"
                        maxLength={2000}
                        value={userPrompt}
                        onChange={(e) => setUserPrompt(e.target.value)}
                        className="w-full h-40 px-4 py-3 text-base rounded-lg bg-input border-none focus:outline-none focus:ring-0 placeholder:text-muted-foreground text-foreground resize-none"
                    />
                </div>
            </div>

            {/* Desktop controls section */}
            <div className="flex flex-nowrap items-center gap-0">
                {/* 左侧控制项 */}
                <div className="flex items-center gap-7">
                    {/* Add Image button */}
                    <button
                        onClick={handleToggleSidebar}
                        className="flex items-center gap-1.5 px-3 py-1.5 rounded-md bg-secondary/30 hover:bg-secondary/50 text-sm transition-colors"
                        title={isSidebarCollapsed ? "Show sidebar" : "Hide sidebar"}
                    >
                        <Image size={16} />
                        <span>
                            {isFluxModel ? `Add Image (${fluxImages.length}/${selectedModel?.image_limit || 2})` :
                                isSidebarCollapsed ? "Add Image" : "Hide Sidebar"}
                        </span>
                    </button>

                    {/* Desktop: all controls */}
                    <div className="flex items-center gap-7">
                        {/* 模型选择 */}
                        <Popover open={isModelPopoverOpen} onOpenChange={setIsModelPopoverOpen}>
                            <PopoverTrigger asChild>
                                <button className="text-sm text-foreground/70 hover:text-foreground flex items-center">
                                    <Cpu size={14} className="mr-1.5" />
                                    Model: {selectedModel?.name || 'Loading...'}
                                </button>
                            </PopoverTrigger>
                            <PopoverContent className="w-96 p-0 bg-popover border-border shadow-xl rounded-xl" align="start">
                                <div className="">
                                    {isLoadingModels ? (
                                        <div className="p-6 text-sm text-gray-500 text-center">Loading models...</div>
                                    ) : availableModels.length === 0 ? (
                                        <div className="p-6 text-sm text-gray-500 text-center">No models available</div>
                                    ) : (
                                        <div className="p-3 space-y-2">
                                            {availableModels.map((model) => (
                                                <button
                                                    key={model.name}
                                                    onClick={() => handleModelSelect(model)}
                                                    className={cn(
                                                        "w-full p-4 rounded-lg text-left transition-all duration-200 group relative border",
                                                        selectedModel?.name === model.name
                                                            ? "bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-600"
                                                            : "hover:bg-gray-50 dark:hover:bg-gray-800/50 border-transparent"
                                                    )}
                                                >
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex items-start space-x-3 flex-1 min-w-0">
                                                            <div className="flex-shrink-0 mt-1">
                                                                <div className="w-7 h-7 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                                    <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                                                                        {model.name.charAt(0).toUpperCase()}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div className="flex-1 min-w-0">
                                                                <div className="font-semibold text-gray-900 dark:text-white text-base truncate">
                                                                    {model.name}
                                                                </div>
                                                                {model.description && (
                                                                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                                                                        {model.description}
                                                                    </div>
                                                                )}
                                                                <div className="flex items-center space-x-2 mt-3">
                                                                    {model.price === 0 ? (
                                                                        <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                                            Free
                                                                        </span>
                                                                    ) : (
                                                                        <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                                                                            {model.price} credits
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {selectedModel?.name === model.name && (
                                                            <div className="flex-shrink-0 ml-3">
                                                                <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center">
                                                                    <svg className="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </PopoverContent>
                        </Popover>

                        {/* Seed设置 */}
                        <Popover>
                            <PopoverTrigger asChild>
                                <button className="text-sm text-foreground/70 hover:text-foreground flex items-center">
                                    <Dices size={14} className="mr-1.5" />
                                    Seed: {seed.length > 7 ? seed.substring(0, 7) : seed}
                                </button>
                            </PopoverTrigger>
                            <PopoverContent className="w-56 p-3 bg-popover border-border text-popover-foreground" align="start">
                                <Label className="block text-xs font-medium mb-2">Random Seed</Label>
                                <div className="flex gap-1">
                                    <input
                                        type="text"
                                        placeholder="Random seed"
                                        value={seed}
                                        onChange={(e) => setSeed(e.target.value)}
                                        className="flex-1 px-2 py-1.5 text-xs rounded bg-input border-input focus:border-ring focus:ring-0"
                                        title="Enter a seed value"
                                        aria-label="Seed value"
                                    />
                                    <button
                                        onClick={generateRandomSeed}
                                        className="bg-secondary hover:bg-secondary/80 px-2 py-1.5 rounded transition-colors"
                                        title="Generate random seed"
                                        aria-label="Generate random seed"
                                    >
                                        <Dices size={14} />
                                    </button>
                                </div>
                                <p className="text-xs text-muted-foreground mt-2">Same seed will produce similar results</p>
                            </PopoverContent>
                        </Popover>

                        {/* Ratio设置 */}
                        <Popover>
                            <PopoverTrigger asChild>
                                <button className="text-sm text-foreground/70 hover:text-foreground flex items-center">
                                    <Ratio size={14} className="mr-1.5" />
                                    Ratio: {aspectRatio}
                                </button>
                            </PopoverTrigger>
                            <PopoverContent className="w-48 p-2 bg-popover border-border text-popover-foreground" align="start">
                                <h4 className="text-xs font-medium mb-2">Aspect Ratio</h4>
                                <div className="grid grid-cols-3 gap-1">
                                    <button
                                        onClick={() => setAspectRatio("16:9" as AspectRatio)}
                                        className={cn(
                                            "p-1.5 rounded text-xs flex flex-col items-center justify-center",
                                            aspectRatio === "16:9"
                                                ? "bg-accent text-accent-foreground"
                                                : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                        )}
                                    >
                                        <div className="w-6 h-3.5 bg-foreground/20 mb-1 rounded"></div>
                                        16:9
                                    </button>
                                    <button
                                        onClick={() => setAspectRatio("9:16" as AspectRatio)}
                                        className={cn(
                                            "p-1.5 rounded text-xs flex flex-col items-center justify-center",
                                            aspectRatio === "9:16"
                                                ? "bg-accent text-accent-foreground"
                                                : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                        )}
                                    >
                                        <div className="w-3.5 h-6 bg-foreground/20 mb-1 rounded"></div>
                                        9:16
                                    </button>
                                    <button
                                        onClick={() => setAspectRatio("1:1" as AspectRatio)}
                                        className={cn(
                                            "p-1.5 rounded text-xs flex flex-col items-center justify-center",
                                            aspectRatio === "1:1"
                                                ? "bg-accent text-accent-foreground"
                                                : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                        )}
                                    >
                                        <div className="w-4 h-4 bg-foreground/20 mb-1 rounded"></div>
                                        1:1
                                    </button>
                                </div>
                            </PopoverContent>
                        </Popover>

                        {/* 高级设置 */}
                        <Popover>
                            <PopoverTrigger asChild>
                                <button className="text-sm text-foreground/70 hover:text-foreground flex items-center">
                                    <Sliders size={14} className="mr-1.5" />
                                    Advanced
                                </button>
                            </PopoverTrigger>
                            <PopoverContent className="w-72 p-3 bg-popover border-border text-popover-foreground" align="start">
                                {/* 负面提示 */}
                                <div className="mb-4">
                                    <Label className="block text-xs font-medium mb-2">Negative Prompt</Label>
                                    <textarea
                                        placeholder="Elements you want to exclude..."
                                        value={negativePrompt}
                                        onChange={(e) => setNegativePrompt(e.target.value)}
                                        className="w-full h-20 px-3 py-2 text-xs rounded-lg bg-input border-input focus:outline-none focus:ring-0 placeholder:text-muted-foreground text-foreground resize-none"
                                        aria-label="Negative prompt"
                                        title="Specify elements to exclude"
                                    />
                                </div>

                                {/* 步数控制 */}
                                <div className="mb-4">
                                    <div className="flex justify-between mb-2">
                                        <Label className="text-xs font-medium">Steps: {steps}</Label>
                                    </div>
                                    <input
                                        type="range"
                                        min="20"
                                        max="50"
                                        value={steps}
                                        onChange={(e) => setSteps(parseInt(e.target.value))}
                                        className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                                        aria-label={`Steps control, current value: ${steps}`}
                                        title="Adjust steps for generation"
                                    />
                                    <div className="flex justify-between mt-1">
                                        <span className="text-xs text-muted-foreground">Faster</span>
                                        <span className="text-xs text-muted-foreground">Quality</span>
                                    </div>
                                </div>

                                {/* 引导缩放 */}
                                <div>
                                    <div className="flex justify-between mb-2">
                                        <Label className="text-xs font-medium">Guidance Scale: {guidanceScale}</Label>
                                    </div>
                                    <input
                                        type="range"
                                        min="1"
                                        max="10"
                                        step="0.5"
                                        value={guidanceScale}
                                        onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                                        className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                                        aria-label={`Guidance scale control, current value: ${guidanceScale}`}
                                        title="Adjust guidance scale for generation"
                                    />
                                    <div className="flex justify-between mt-1">
                                        <span className="text-xs text-muted-foreground">Creativity</span>
                                        <span className="text-xs text-muted-foreground">Accuracy</span>
                                    </div>
                                </div>
                            </PopoverContent>
                        </Popover>
                    </div>
                </div>

                {/* 右侧生成按钮 */}
                <div className="ml-auto">
                    <button
                        onClick={handleGenerate}
                        disabled={isDisabled}
                        className={`px-6 py-2.5 rounded-lg text-white font-medium flex items-center
                            ${isDisabled ? 'bg-gray-400 cursor-not-allowed' : 'text-white animate-gradient-bg bg-gradient-to-r from-red-600 via-orange-500 via-blue-500 to-pink-600'}
                            ${isGenerating ? 'bg-primary/50' : 'bg-primary hover:bg-primary/80'}`}
                        title={isFluxLimitReached ? `FLUX models limited to ${fluxUsageLimit?.limit || 2} uses for non-members` : undefined}
                    >
                        {isGenerating ? (
                            <>
                                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                                Generating...
                            </>
                        ) : isFluxLimitReached ? (
                            <>
                                <span>Limit Reached</span>
                            </>
                        ) : (
                            <>
                                <Sparkles size={18} className="mr-2" />
                                Generate
                            </>
                        )}
                    </button>
                </div>
            </div>

            {/* 错误信息和FLUX限制提示 */}
            {error && (
                <div className="mt-2 px-3 py-2 bg-destructive/10 text-destructive text-sm rounded border border-destructive/20">
                    {error}
                </div>
            )}
            {isFluxLimitReached && (
                <div className="mt-2 px-3 py-2 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 text-sm rounded border border-orange-200 dark:border-orange-800">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            Free users can only use FLUX models {fluxUsageLimit?.limit || 2} times. Please upgrade to membership for unlimited access.
                        </div>
                        <button
                            onClick={() => router.push('/membership')}
                            className="ml-3 px-3 py-1.5 bg-orange-600 hover:bg-orange-700 text-white text-xs font-medium rounded transition-colors"
                        >
                            Upgrade Now
                        </button>
                    </div>
                </div>
            )}
        </div >
    );

    // 使用 Responsive 组件根据设备类型渲染不同的控制面板
    return (
        <Responsive
            mobile={mobileControlPanel}
            tablet={tabletControlPanel}
            desktop={desktopControlPanel}
        />
    );
}