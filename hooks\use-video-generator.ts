"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { GenerationType, QualityMode, Duration, AspectRatio } from "@/types/video";
import { mapUIToAPIDefinition, mapUIToAPIDuration } from "@/types/video";
import { generationApi } from "@/lib/api/generation";
import { ApiError } from "@/lib/api/client";
import useVideoTasksStore from "@/store/useVideoTasksStore";
import { useAuth } from "@/contexts/auth-context";
import { Model } from "@/types/model";

const gen_img_size_limit = 10 * 1024 * 1024 // 10MB

// 预签名URL响应接口
interface PresignedUrlData {
    url: string;
    key: string;
    fields: Record<string, string>;
    publicUrl: string;
    expires: string;
}

export function useVideoGenerator() {
    const [generationType, setGenerationType] = useState<GenerationType>(GenerationType.TextToVideo)
    const [creativityLevel, setCreativityLevel] = useState(5)
    const [quality, setQuality] = useState<QualityMode>("standard")
    const [duration, setDuration] = useState<Duration>("5")
    const [aspectRatio, setAspectRatio] = useState<AspectRatio>("9:16")
    const [prompt, setPrompt] = useState("")
    const [negativePrompt, setNegativePrompt] = useState("")
    const [steps, setSteps] = useState(40)
    const [seed, setSeed] = useState("")
    const [images, setImages] = useState<string[]>([])
    const [imageFiles, setImageFiles] = useState<File[]>([]) // 存储实际的文件对象
    const [isGenerating, setIsGenerating] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [referImageUrl, setReferImageUrl] = useState<string | null>(null) // 存储上传后的参考图片URL
    const [presignedData, setPresignedData] = useState<PresignedUrlData | null>(null) // 存储预签名URL数据
    const [isLoadingPresignedUrl, setIsLoadingPresignedUrl] = useState(false) // 预签名URL加载状态

    // 使用状态管理替代原来的本地状态
    const {
        videoTasks,
        currentTask,
        isLoading,
        fetchUserTasks,
        setSelectedTask
    } = useVideoTasksStore();

    const { isAuthenticated } = useAuth();

    // 页面加载时初始化数据
    useEffect(() => {
        isAuthenticated && fetchUserTasks();
    }, [fetchUserTasks, isAuthenticated]);

    // 获取预签名URL
    const getPresignedUrl = useCallback(async (file: File): Promise<PresignedUrlData> => {
        setIsLoadingPresignedUrl(true);
        try {
            const response = await fetch('/api/upload/presign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: file.name,
                    fileType: file.type,
                    fileSize: file.size,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '获取上传URL失败');
            }

            const { data } = await response.json();
            return data;
        } catch (error) {
            console.error('获取预签名URL错误:', error);
            throw new Error('获取预签名URL失败');
        } finally {
            setIsLoadingPresignedUrl(false);
        }
    }, []);

    // 当用户选择图片后，预先获取预签名URL
    useEffect(() => {
        const fetchPresignedUrl = async () => {
            if (imageFiles.length > 0 && !presignedData && !referImageUrl) {
                try {
                    const data = await getPresignedUrl(imageFiles[0]);
                    setPresignedData(data);
                } catch (error) {
                    console.error('预获取预签名URL失败:', error);
                    // 这里不设置错误状态，因为这只是预加载，真正上传时会再次尝试
                }
            }
        };

        fetchPresignedUrl();
    }, [imageFiles, presignedData, referImageUrl, getPresignedUrl]);

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files
        if (!files) return

        // 验证文件大小和类型
        const validFiles = Array.from(files).filter((file) => {
            const isValidType = file.type.startsWith("image/")
            const isValidSize = file.size <= gen_img_size_limit
            return isValidType && isValidSize
        })

        if (validFiles.length !== files.length) {
            setError("部分文件被跳过。请确保所有文件都是10MB以下的图片。")
        }

        // 限制最多只能上传1张图片
        if (images.length + validFiles.length > 1) {
            setError("最多只能上传1张参考图片。")
            // 如果已有图片，则不添加新图片
            if (images.length > 0) return
            // 如果没有图片，则只取第一张
            validFiles.splice(1)
        }

        // 清除之前的图片和预签名数据
        if (images.length > 0) {
            images.forEach(url => URL.revokeObjectURL(url))
            setImages([])
            setImageFiles([])
            setReferImageUrl(null)
            setPresignedData(null)
        }

        const newImages = validFiles.map((file) => URL.createObjectURL(file))
        setImages(newImages)
        setImageFiles(validFiles)
        setReferImageUrl(null) // 重置已上传的URL
        setPresignedData(null) // 重置预签名数据
    }

    const removeImage = (index: number) => {
        // 释放对象URL
        URL.revokeObjectURL(images[index])
        setImages((prev) => prev.filter((_, i) => i !== index))
        setImageFiles((prev) => prev.filter((_, i) => i !== index))
        setReferImageUrl(null) // 重置已上传的URL
        setPresignedData(null) // 重置预签名数据
    }

    // 上传图片到Cloudflare R2
    const uploadImageToR2 = async (file: File): Promise<string> => {
        try {
            // 如果已经有预签名数据，直接使用
            let uploadData = presignedData;

            // 如果没有预签名数据或已过期，重新获取
            if (!uploadData || new Date(uploadData.expires) <= new Date()) {
                uploadData = await getPresignedUrl(file);
                setPresignedData(uploadData);
            }

            // 使用预签名URL上传文件
            const uploadResponse = await fetch(uploadData.url, {
                method: 'PUT',
                headers: {
                    'Content-Type': file.type,
                },
                body: file,
            });

            if (!uploadResponse.ok) {
                throw new Error('上传图片失败');
            }

            // 返回公共访问URL
            return uploadData.publicUrl;
        } catch (error) {
            console.error('上传图片错误:', error);
            throw new Error('上传参考图片失败');
        }
    };

    /**
     * 生成视频
     *
     * 注意：此函数假设UI层已经进行了基本验证，如：
     * - 模型已选择
     * - 图片上传已完成
     * - 必要的输入已提供
     *
     * @param modelId 选中的模型ID
     * @param selectedEffect 选中的效果
     * @returns 生成的任务信息，失败时返回null
     */
    const generateVideo = async (model: Model, selectedEffect?: any) => {
        try {
            setIsGenerating(true)
            setError(null)

            // 处理参考图片
            const referImgUrl = await prepareReferenceImage()
            if (referImgUrl === null && images.length > 0) {
                // prepareReferenceImage已设置错误信息
                return null
            }

            // 准备请求参数
            const requestBody = prepareRequestBody(model, referImgUrl, selectedEffect)

            // 发送API请求
            const task = await generationApi.createTask(requestBody)

            // 更新任务列表
            await fetchUserTasks()

            return task
        } catch (err) {
            const errorMessage = err instanceof ApiError || err instanceof Error
                ? err.message
                : "An unknown error occurred while generating the video"
            setError(errorMessage)
            return null
        } finally {
            setIsGenerating(false)
        }
    }

    /**
     * 准备参考图片 - 确保图片已上传到Cloudflare
     * @returns 参考图片URL或null（无图片或处理失败）
     */
    const prepareReferenceImage = async (): Promise<string | null> => {
        // 无图片情况
        if (images.length === 0) {
            return null
        }

        // 检查图片是否为base64格式（未上传完成）
        if (images[0].startsWith('data:')) {
            if (imageFiles.length === 0) {
                setError("Image is processing, please wait a moment and try again")
                return null
            }

            try {
                // 上传图片到Cloudflare
                const cloudflareUrl = await uploadImageToR2(imageFiles[0])

                // 更新状态
                setReferImageUrl(cloudflareUrl)
                setImages([cloudflareUrl])

                return cloudflareUrl
            } catch (error) {
                setError("Image upload failed, please try again")
                return null
            }
        }

        // 图片已上传情况
        if (imageFiles.length > 0 && !referImageUrl) {
            // 需要上传图片
            const cloudflareUrl = await uploadImageToR2(imageFiles[0])
            setReferImageUrl(cloudflareUrl)
            return cloudflareUrl
        } else if (referImageUrl) {
            // 已有上传后的URL
            return referImageUrl
        } else {
            // 使用images中的URL（已是Cloudflare URL）
            return images[0]
        }
    }

    /**
     * 准备请求体
     * @param modelId 模型ID
     * @param referImgUrl 参考图片URL
     * @param selectedEffect 选中的效果
     * @returns 请求体对象
     */
    const prepareRequestBody = (model: Model, referImgUrl: string | null, selectedEffect?: any): any => {
        // 生成随机种子（如果未提供）
        const usedSeed = seed.trim() ? parseInt(seed) : Math.floor(Math.random() * 1000000)

        // 基础请求参数
        const requestBody: any = {
            prompt: prompt,
            gen_type: model.model_type,
            negative_prompt: negativePrompt,
            guidance_scale: creativityLevel / 10,
            steps: steps,
            seed: usedSeed,
            definition: mapUIToAPIDefinition(quality),
            duration: mapUIToAPIDuration(duration),
            ratio: aspectRatio,
            model_id: model.id
        }

        // 添加参考图片URL（如果有）
        if (referImgUrl) {
            requestBody.refer_img_url = referImgUrl
        }

        // 添加效果相关参数（如果有）
        if (selectedEffect) {
            requestBody.effect_name = selectedEffect.name

            if (selectedEffect.trigger_words && Array.isArray(selectedEffect.trigger_words)) {
                requestBody.trigger_words = selectedEffect.trigger_words
            }
        }

        return requestBody
    }

    // 从任务ID加载参数
    const loadTaskParams = useCallback(async (taskId: string) => {
        if (!taskId) return;

        try {
            setIsGenerating(true);
            setError(null);

            // 获取任务详情
            const task = await generationApi.getTaskDetail(taskId);

            if (!task || !task.input_params) {
                throw new Error("Unable to get task parameters");
            }

            // 设置参数到表单
            const params = task.input_params;

            // 设置提示词
            if (params.prompt) setPrompt(params.prompt);
            if (params.negative_prompt) setNegativePrompt(params.negative_prompt);

            // 设置技术参数
            if (params.gen_type) setGenerationType(params.gen_type);
            if (params.guidance_scale) setCreativityLevel(Math.round(params.guidance_scale * 10)); // 将0-10的值映射到0-100
            if (params.steps) setSteps(params.steps);
            if (params.seed) setSeed(params.seed.toString());

            // 设置输出参数
            if (params.definition) setQuality(params.definition === "480P" ? "standard" : "high");
            if (params.duration) {
                // 支持5s, 8s, 10s
                if (params.duration === "5s") setDuration("5");
                else if (params.duration === "8s") setDuration("8");
                else setDuration("10");
            }
            if (params.ratio) setAspectRatio(params.ratio as AspectRatio);

            // 设置参考图片
            if (params.refer_img_url) {
                setReferImageUrl(params.refer_img_url);
                setImages([params.refer_img_url]);
            }

            return task;
        } catch (err) {
            const errorMessage = err instanceof ApiError || err instanceof Error
                ? err.message
                : "Error loading task parameters";
            setError(errorMessage);
            return null;
        } finally {
            setIsGenerating(false);
        }
    }, [setPrompt, setNegativePrompt, setGenerationType, setCreativityLevel, setSteps, setSeed, setQuality, setDuration, setAspectRatio]);

    return {
        generationType,
        setGenerationType,
        creativityLevel,
        setCreativityLevel,
        quality,
        setQuality,
        duration,
        setDuration,
        aspectRatio,
        setAspectRatio,
        prompt,
        setPrompt,
        negativePrompt,
        setNegativePrompt,
        steps,
        setSteps,
        seed,
        setSeed,
        images,
        handleImageUpload,
        removeImage,
        generateVideo,
        isGenerating,
        error,
        isLoadingPresignedUrl,
        // 使用状态管理的数据
        videoTasks,
        currentTask,
        isLoading,
        fetchUserTasks,
        setSelectedTask,
        loadTaskParams
    }
}