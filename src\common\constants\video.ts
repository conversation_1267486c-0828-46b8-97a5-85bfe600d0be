export enum NSFWLevel {
    PG = 'PG', // Safe for work. No naughty stuff
    PG_13 = 'PG-13', // Revealing clothing, violence, or light gore
    R = 'R', // Adult themes and situations, partial nudity, graphic violence, or death
    X = 'X', // Graphic nudity, adult objects, or settings
    XXX = 'XXX', // Overtly sexual or disturbing graphic content
}

export enum GenType {
    TEXT_TO_VIDEO = 'text-to-video',
    VIDEO_TO_VIDEO = 'video-to-video',
    IMAGE_TO_VIDEO = 'image-to-video'
}

export enum VideoTaskStatus {
    PENDING = 'pending',
    QUEUED = 'queued',
    PROCESSING = 'processing',
    COMPLETED = 'completed',
    FAILED = 'failed',
    CANCELED = 'canceled',
}

export enum VideoDefinition {
    SD = '480P',
    HD = '720P',
}

export enum VideoDuration {
    SHORT = '5',
    MEDIUM = '8',
    LONG = '10',
}

export enum VideoRatio {
    NARROW = '9:16',
    WIDE = '16:9',
    SQUARE = '1:1',
}

export type VideoTaskErrorLog = {
    message: string;
    code?: string;
    stack_trace?: string;
    failed_step?: string;
    retryable?: boolean;
}

export type VideoTaskOutputResult = {
    video_url: string;
    thumbnail_url?: string;
    duration_seconds?: number;
    file_size_mb?: number;
    format?: string;
    checksum?: string;
}

export type GenVideoInputParams = {
    gen_type: GenType;
    prompt: string;
    negative_prompt: string;
    guidance_scale: number;
    steps: number;
    seed: number;
    definition: VideoDefinition;
    duration: VideoDuration;
    ratio: VideoRatio;
}
