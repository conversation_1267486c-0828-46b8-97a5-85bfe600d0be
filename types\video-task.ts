import { GenerationType } from "./video";

// 定义任务状态类型
export type TaskStatus = "pending" | "queued" | "processing" | "completed" | "failed" | "canceled";

// 定义任务类型
export interface VideoTask {
    id: string;
    user_id: string;
    status: TaskStatus;
    progress: number;
    duration_estimate: number; // 预计处理时间（分钟）
    input_params: {
        prompt: string;
        gen_type: GenerationType;
        negative_prompt?: string;
        guidance_scale?: number;
        steps?: number;
        seed?: number;
        definition?: "480P" | "720P";
        duration?: "5s" | "8s" | "10s";
        ratio?: "9:16" | "16:9" | "1:1";
        refer_img_url?: string;
        video_url?: string;
        audio_url?: string;
        model_id?: string;
        model_name?: string;
        effect_name?: string;
    };
    output_result?: {
        url?: string;
        file_name?: string;
        file_size?: number;
        video_url: string;
        content_type?: string;
        duration_seconds?: number;
        file_size_mb?: number;
        format?: string;
    };
    created_at: string;
    queued_at?: string;
    started_at?: string;
    completed_at?: string;
    last_activity_at: string;
}