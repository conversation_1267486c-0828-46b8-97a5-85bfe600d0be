import { IsString, <PERSON>NotEmpty, IsOptional, <PERSON>Enum, <PERSON>N<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { GenType, VideoDefinition, VideoDuration, VideoRatio } from 'src/common/constants/video';


// 定义视频生成请求的参数类
export class GenVideoRequestDto {
    @IsString()
    @IsOptional()
    model_id: string;

    @IsString()
    @IsOptional()
    effect_name: string;

    @IsString()
    @IsOptional()
    prompt: string;

    @IsEnum(GenType)
    @IsNotEmpty()
    gen_type: GenType;

    @IsString()
    @IsOptional()
    negative_prompt?: string;

    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(50)
    guidance_scale?: number;

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Max(100)
    steps?: number;

    @IsNumber()
    @IsOptional()
    seed?: number;

    @IsEnum(VideoDefinition)
    @IsOptional()
    definition?: VideoDefinition;

    @IsString()
    @IsOptional()
    duration?: string;

    @IsEnum(VideoRatio)
    @IsOptional()
    ratio?: VideoRatio;

    @IsString()
    @IsOptional()
    refer_img_url?: string;

    @IsString()
    @IsOptional()
    start_image_url?: string;

    @IsString()
    @IsOptional()
    end_image_url?: string;
}

export class TickProgressDto {
    @IsString()
    @IsNotEmpty()
    task_id: string;

    @IsNumber()
    @IsNotEmpty()
    progress: number;
}

