"use client"

import { useState, useEffect } from "react"
import { ArrowR<PERSON>, Send, Plus, Wand2, Film, Camera, Video, Play, Lock, Key, CheckCircle2, Info, Clock, XCircle, Shield<PERSON>heck, <PERSON>rk<PERSON>, History } from "lucide-react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { qwenClient } from "@/lib/qwen-client"
import { createClient } from "@/lib/supabase/client"
import "../../../styles/coming-soon-animations.css"

interface SessionHistory {
  id: string
  prompt: string
  created_at: string
  status: string
}

export default function ConversationPage() {
  const router = useRouter()
  const [prompt, setPrompt] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchResult, setSearchResult] = useState<string | null>(null)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  
  // Invitation code state
  const [invitationCode, setInvitationCode] = useState("")
  const [isCodeVerified, setIsCodeVerified] = useState(false)
  const [isCodeError, setIsCodeError] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")
  const [isVerifying, setIsVerifying] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [isCheckingAccess, setIsCheckingAccess] = useState(true)
  
  // History state
  const [showHistory, setShowHistory] = useState(false)
  const [sessionHistory, setSessionHistory] = useState<SessionHistory[]>([])
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)

  const supabase = createClient()

  // Check user access on component mount
  useEffect(() => {
    checkUserAccess()
  }, [])

  const checkUserAccess = async () => {
    try {
      setIsCheckingAccess(true)
      
      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        // Redirect to login if not authenticated
        router.push('/auth/login')
        return
      }

      // Check localStorage first to avoid unnecessary API calls
      try {
        const cachedAccess = localStorage.getItem('movie_agents_access')
        if (cachedAccess === 'granted') {
          setIsCodeVerified(true)
          setIsCheckingAccess(false)
          return
        }
      } catch (storageError) {
        // localStorage might not be available in some environments
        console.log('localStorage not available:', storageError)
      }

      // Check if user has access via API
      const response = await fetch('/api/movie-agents/invitation/verify')
      if (response.ok) {
        const data = await response.json()
        if (data.hasAccess) {
          setIsCodeVerified(true)
          // Cache the access state
          try {
            localStorage.setItem('movie_agents_access', 'granted')
          } catch (storageError) {
            console.log('Could not cache access state:', storageError)
          }
        } else {
          setIsCodeVerified(false)
          // Clear any stale cache
          try {
            localStorage.removeItem('movie_agents_access')
          } catch (storageError) {
            console.log('Could not clear cache:', storageError)
          }
        }
      }
    } catch (error) {
      console.error('Error checking access:', error)
      // Clear cache on error
      try {
        localStorage.removeItem('movie_agents_access')
      } catch (storageError) {
        console.log('Could not clear cache on error:', storageError)
      }
    } finally {
      setIsCheckingAccess(false)
    }
  }

  const loadSessionHistory = async () => {
    try {
      setIsLoadingHistory(true)
      const response = await fetch('/api/movie-agents/sessions?limit=10')
      if (response.ok) {
        const data = await response.json()
        setSessionHistory(data.sessions)
      }
    } catch (error) {
      console.error('Error loading history:', error)
    } finally {
      setIsLoadingHistory(false)
    }
  }

  const saveSession = async (sessionData: any) => {
    try {
      console.log('💾 尝试保存会话:', sessionData)
      const response = await fetch('/api/movie-agents/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sessionData),
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ 会话保存成功:', data.session.id)
        setCurrentSessionId(data.session.id)
        return data.session.id
      } else {
        const errorData = await response.json()
        console.error('❌ 会话保存失败:', errorData)
        return null
      }
    } catch (error) {
      console.error('❌ 会话保存错误:', error)
      return null
    }
  }
  
  const handleSubmitPrompt = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!prompt.trim()) return
    
    console.log('🚀 提交prompt:', prompt)
    setIsSubmitting(true)
    
    try {
      // Call Qwen API for web search (but don't wait for it to complete navigation)
      const searchPromise = qwenClient.search(prompt);
      
      // Save initial session (but don't wait for it to complete navigation)
      const sessionPromise = saveSession({
        prompt: prompt,
        status: 'active'
      })
      
      // Navigate immediately without waiting for search or session save
      const params = new URLSearchParams({
        prompt: prompt
      })
      const navigationUrl = `/agents/think-plan?${params.toString()}`
      console.log('🔗 立即导航到:', navigationUrl)
      
      // Navigate immediately
      router.push(navigationUrl)
      
      // Handle search and session save in background
      try {
        const [searchResponse, sessionId] = await Promise.allSettled([searchPromise, sessionPromise])
        
        if (searchResponse.status === 'fulfilled') {
          console.log('🔍 搜索结果:', searchResponse.value.result)
          setSearchResult(searchResponse.value.result)
        }
        
        if (sessionId.status === 'fulfilled' && sessionId.value) {
          console.log('💾 会话保存成功:', sessionId.value)
          setCurrentSessionId(sessionId.value)
        }
      } catch (backgroundError) {
        console.log('⚠️ 后台处理完成，但有部分错误:', backgroundError)
      }
      
    } catch (error) {
      console.error("❌ 处理错误:", error);
      // Even if there's an error, still navigate to think-plan
      const params = new URLSearchParams({ prompt: prompt })
      const navigationUrl = `/agents/think-plan?${params.toString()}`
      console.log('🔗 错误后导航到:', navigationUrl)
      router.push(navigationUrl)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Function to verify invitation code
  const verifyInvitationCode = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!invitationCode.trim()) return
    
    setIsVerifying(true)
    setIsCodeError(false)
    setErrorMessage("")
    
    try {
      const response = await fetch('/api/movie-agents/invitation/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: invitationCode }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // Cache the access state
        try {
          localStorage.setItem('movie_agents_access', 'granted')
        } catch (storageError) {
          console.log('Could not cache access state:', storageError)
        }
        
        setShowSuccessAnimation(true)
        // Wait for animation to complete before removing overlay
        setTimeout(() => {
          setIsCodeVerified(true)
        }, 1500)
      } else {
        setIsCodeError(true)
        setErrorMessage(data.error || 'Invalid invitation code')
        setIsVerifying(false)
      }
    } catch (error) {
      console.error('Error verifying invitation code:', error)
      setIsCodeError(true)
      setErrorMessage('Network error. Please try again.')
      setIsVerifying(false)
    }
  }

  const loadHistorySession = (session: SessionHistory) => {
    router.push(`/agents/think-plan?sessionId=${session.id}`)
  }

  if (isCheckingAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-muted-foreground">Checking access permissions...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex flex-col">
      {/* Header */}
      <header className="border-b p-4">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <h1 className="text-xl font-semibold">ReelMind Studio</h1>
          <div className="flex items-center gap-3">
            {isCodeVerified && (
              <button
                onClick={() => {
                  setShowHistory(!showHistory)
                  if (!showHistory && sessionHistory.length === 0) {
                    loadSessionHistory()
                  }
                }}
                className="px-3 py-2 bg-secondary/10 hover:bg-secondary/20 text-secondary-foreground rounded-md text-sm font-medium transition-colors flex items-center gap-2"
              >
                <History className="h-4 w-4" />
                History
              </button>
            )}
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col max-w-screen-xl mx-auto w-full px-4 py-8">
        {/* History sidebar */}
        {showHistory && (
          <div className="mb-6 bg-white rounded-lg border shadow-sm">
            <div className="p-4 border-b">
              <h3 className="font-medium flex items-center gap-2">
                <History className="h-4 w-4" />
                Recent Sessions
              </h3>
            </div>
            <div className="p-4">
              {isLoadingHistory ? (
                <div className="text-center py-4">
                  <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Loading history...</p>
                </div>
              ) : sessionHistory.length > 0 ? (
                <div className="space-y-2">
                  {sessionHistory.map((session) => (
                    <div
                      key={session.id}
                      onClick={() => loadHistorySession(session)}
                      className="p-3 rounded-lg border hover:bg-secondary/10 cursor-pointer transition-colors"
                    >
                      <p className="text-sm font-medium truncate">{session.prompt}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(session.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No previous sessions found
                </p>
              )}
            </div>
          </div>
        )}

        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-3xl shadow-lg border-primary/10">
            <CardContent className="p-6">
              <h2 className="text-2xl font-bold mb-6 text-center">How can we help with your video project today?</h2>
              
              <form onSubmit={handleSubmitPrompt} className="space-y-6">
                <div className="relative">
                  <textarea 
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Create a promotional video for a sustainable fashion brand that showcases their eco-friendly manufacturing process"
                    className="w-full min-h-[120px] p-4 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/30 resize-none text-sm"
                  />
                  <button
                    type="submit"
                    disabled={isSubmitting || !prompt.trim()}
                    className={`absolute bottom-3 right-3 p-2 rounded-md ${
                      isSubmitting || !prompt.trim()
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "bg-primary text-primary-foreground hover:bg-primary/90"
                    } transition-colors`}
                    aria-label="Send prompt"
                  >
                    {isSubmitting ? (
                      <div className="animate-spin h-5 w-5 border-2 border-primary-foreground border-t-transparent rounded-full" />
                    ) : (
                      <Send className="h-5 w-5" />
                    )}
                  </button>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 text-primary-foreground transition-colors"
                      aria-label="Add attachment"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                    <button
                      type="button" 
                      className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 text-primary-foreground transition-colors"
                      aria-label="Generate with AI"
                    >
                      <Wand2 className="h-4 w-4" />
                    </button>
                    <span className="text-xs text-muted-foreground ml-2">
                      Generate with AI
                    </span>
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isSubmitting || !prompt.trim()}
                    className={`px-4 py-2 rounded-md flex items-center gap-2 text-sm font-medium ${
                      isSubmitting || !prompt.trim()
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "bg-primary text-primary-foreground hover:bg-primary/90"
                    } transition-colors`}
                  >
                    {isSubmitting ? "Processing..." : "Create Video Project"}
                    {!isSubmitting && <ArrowRight className="h-4 w-4" />}
                  </button>
                </div>
              </form>
              
              {searchResult && (
                <div className="mt-4 p-4 bg-blue-50/50 rounded-lg text-sm text-muted-foreground border border-blue-100">
                  <p className="font-medium text-blue-600 mb-1">AI Research Results:</p>
                  <p>{searchResult}</p>
                </div>
              )}
              
              <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Film className="h-4 w-4 text-blue-500" />
                      <h3 className="font-medium">Brand Video Campaign</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Professional promotional videos for your brand or product</p>
                  </CardContent>
                </Card>
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Play className="h-4 w-4 text-green-500" />
                      <h3 className="font-medium">Social Media Reels</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Short-form vertical videos optimized for social platforms</p>
                  </CardContent>
                </Card>
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Camera className="h-4 w-4 text-amber-500" />
                      <h3 className="font-medium">Product Showcase</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Highlight features and benefits of your products with cinematic visuals</p>
                  </CardContent>
                </Card>
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Video className="h-4 w-4 text-purple-500" />
                      <h3 className="font-medium">Explainer Video</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Clear, engaging explanations of your service or concept</p>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-6 text-xs text-center text-muted-foreground">
                <p>Powered by Qwen AI with real-time internet search capabilities</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Invitation Code Overlay */}
      {!isCodeVerified && (
        <div className="fixed inset-0 z-[100] backdrop-blur-md bg-background/70 flex flex-col items-center justify-center overflow-hidden px-6">
          <div className="relative max-w-md w-full mx-auto px-6 sm:px-8 py-8 sm:py-10 rounded-2xl bg-gradient-to-b from-background/90 to-background/80 border border-primary/20 shadow-xl backdrop-blur-md md:max-w-lg">
            {/* Decorative elements */}
            <div className="absolute -top-10 -left-10">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 opacity-40 rounded-full blur-xl"></div>
            </div>
            <div className="absolute -bottom-10 -right-10">
              <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-cyan-500 opacity-40 rounded-full blur-xl"></div>
            </div>
            
            {/* Success animation overlay */}
            {showSuccessAnimation && (
              <div className="absolute inset-0 z-20 bg-gradient-to-b from-background/80 to-background/80 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                    <CheckCircle2 className="h-10 w-10 text-green-500 animate-pulse" />
                  </div>
                  <h3 className="text-xl font-bold text-green-500 mb-1">Access Granted!</h3>
                  <p className="text-sm text-muted-foreground">Welcome to Movie Agents Studio</p>
                </div>
              </div>
            )}
            
            {/* Content */}
            <div className="text-center relative z-10">
              <div className="inline-flex items-center justify-center mb-6">
                <Lock className="h-8 w-8 text-primary mr-2" />
                <Key className="h-8 w-8 text-amber-500" />
              </div>
              
              {/* Early Access badge - positioned better */}
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary-foreground text-xs font-medium mb-4">
                  <Sparkles className="h-3 w-3 mr-1" />
                  <span>Early Access</span>
              </div>
              
              <h2 className="text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-500">
                Exclusive Access
              </h2>
              
              <p className="text-md text-muted-foreground mb-6">
                Enter your invitation code to access our Movie Agents Studio before its public release.
              </p>
              
              <form onSubmit={verifyInvitationCode} className="mb-6">
                <div className="relative mb-4">
                  <input
                    type="text"
                    value={invitationCode}
                    onChange={(e) => {
                      setInvitationCode(e.target.value)
                      setIsCodeError(false)
                      setErrorMessage("")
                    }}
                    placeholder="Enter invitation code"
                    className={`w-full px-4 py-3 rounded-md bg-background border ${
                      isCodeError ? 'border-red-500' : 'border-primary/20'
                    } focus:outline-none focus:ring-2 focus:ring-primary/30 placeholder-muted-foreground/50 text-center uppercase tracking-wider`}
                    maxLength={20}
                  />
                  {isCodeError && (
                    <div className="flex items-center justify-center gap-1 mt-2 text-red-500 text-sm">
                      <XCircle className="h-4 w-4" />
                      <span>{errorMessage}</span>
                    </div>
                  )}
                </div>
                
                <button
                  type="submit"
                  disabled={isVerifying || !invitationCode.trim()}
                  className={`w-full rounded-md py-3 font-medium transition-all duration-200 ${
                    isVerifying || !invitationCode.trim()
                      ? 'bg-muted text-muted-foreground cursor-not-allowed'
                      : 'bg-primary text-primary-foreground hover:bg-primary/90'
                  }`}
                >
                  {isVerifying ? (
                    <div className="flex items-center justify-center gap-2">
                      <Clock className="h-4 w-4 animate-spin" />
                      <span>Verifying...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <ShieldCheck className="h-4 w-4" />
                      <span>Unlock Access</span>
                    </div>
                  )}
                </button>
              </form>
              
              {/* Available invitation codes hint */}
              <div className="text-left p-4 bg-primary/5 rounded-lg mb-6">
                <p className="text-xs font-medium mb-2 text-primary-foreground">Available invitation codes for testing:</p>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>REELMIND2024</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>BETAUSER</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>AI4MOVIES</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>EARLYACCESS</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>VIPCODE</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>TESTCODE1</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>TESTCODE2</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Key className="h-3 w-3 text-amber-500" />
                    <span>TESTCODE3</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground/70 mt-2">
                  ⚠️ Each code can only be used once!
                </p>
              </div>
              
              <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                <Info className="h-3 w-3" />
                <span>Need a code? Contact our team for early access.</span>
              </div>
            </div>
          </div>
          
          {/* Background effect */}
          <div className="absolute inset-0 -z-10 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-gradient-to-tr from-cyan-500/20 to-indigo-500/20 rounded-full blur-3xl"></div>
          </div>
        </div>
      )}
    </div>
  )
} 