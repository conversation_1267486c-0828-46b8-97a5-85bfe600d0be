'use client';

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DynamicModelParams } from '@/components/ui/dynamic-model-params';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, CheckCircle } from 'lucide-react';

// 创建QueryClient实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5分钟
      gcTime: 1000 * 60 * 10, // 10分钟
    },
  },
});

// 测试用的模型ID列表
const TEST_MODELS = [
  'fal-ai/kling-video/v1.6/pro/image-to-video',
  'fal-ai/pika/v2/turbo/text-to-video',
  'fal-ai/cogvideox-5b/video-to-video',
  'fal-ai/wan-flf2v',
  'fal-ai/hunyuan-video',
  'fal-ai/veo3',
];

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
}

const DynamicParamsTestContent: React.FC = () => {
  const [selectedModelId, setSelectedModelId] = useState<string>(TEST_MODELS[0]);
  const [paramValues, setParamValues] = useState<Record<string, any>>({});
  const [paramErrors, setParamErrors] = useState<Record<string, string>>({});
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const handleParamChange = (values: Record<string, any>) => {
    setParamValues(values);
    setParamErrors({});
    setTestResult(null);
  };

  const handleValidateParams = async () => {
    if (!selectedModelId || Object.keys(paramValues).length === 0) {
      setTestResult({
        success: false,
        message: 'Please select a model and set some parameters first'
      });
      return;
    }

    setIsValidating(true);
    try {
      const { modelConfigApi } = await import('@/lib/api/model-config');
      const validation = await modelConfigApi.validateModelParams({
        modelId: selectedModelId,
        params: paramValues
      });

      if (validation.isValid) {
        setTestResult({
          success: true,
          message: 'Parameters validation passed!',
          data: validation.validatedParams
        });
        setParamErrors({});
      } else {
        setTestResult({
          success: false,
          message: `Validation failed: ${validation.errors.join(', ')}`,
          data: validation.errors
        });
        // 设置参数错误
        const errors: Record<string, string> = {};
        validation.errors.forEach((error, index) => {
          errors[`error_${index}`] = error;
        });
        setParamErrors(errors);
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleTestGeneration = async () => {
    if (!selectedModelId || Object.keys(paramValues).length === 0) {
      setTestResult({
        success: false,
        message: 'Please select a model and set some parameters first'
      });
      return;
    }

    setTestResult({
      success: true,
      message: 'This would normally trigger video generation with the dynamic parameters',
      data: {
        modelId: selectedModelId,
        dynamicParams: paramValues
      }
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Dynamic Model Parameters Test</h1>
        <p className="text-muted-foreground">
          Test the dynamic model parameter configuration system
        </p>
      </div>

      {/* Model Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Model Selection</CardTitle>
          <CardDescription>
            Choose a model to test its dynamic parameters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="model-select">Select Model</Label>
            <select
              id="model-select"
              value={selectedModelId}
              onChange={(e) => {
                setSelectedModelId(e.target.value);
                setParamValues({});
                setParamErrors({});
                setTestResult(null);
              }}
              className="w-full p-2 border rounded-md"
            >
              {TEST_MODELS.map((modelId) => (
                <option key={modelId} value={modelId}>
                  {modelId}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">Selected Model:</Badge>
            <code className="text-sm bg-muted px-2 py-1 rounded">
              {selectedModelId}
            </code>
          </div>
        </CardContent>
      </Card>

      {/* Dynamic Parameters */}
      <DynamicModelParams
        modelId={selectedModelId}
        values={paramValues}
        onChange={handleParamChange}
        errors={paramErrors}
      />

      {/* Current Parameters Display */}
      <Card>
        <CardHeader>
          <CardTitle>Current Parameters</CardTitle>
          <CardDescription>
            View the current parameter values
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-md text-sm overflow-auto">
            {JSON.stringify(paramValues, null, 2)}
          </pre>
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
          <CardDescription>
            Test parameter validation and generation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Button 
              onClick={handleValidateParams}
              disabled={isValidating}
              variant="outline"
            >
              {isValidating ? 'Validating...' : 'Validate Parameters'}
            </Button>
            <Button 
              onClick={handleTestGeneration}
              disabled={Object.keys(paramValues).length === 0}
            >
              Test Generation
            </Button>
          </div>

          {/* Test Result */}
          {testResult && (
            <Alert className={testResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              {testResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={testResult.success ? 'text-green-800' : 'text-red-800'}>
                <div className="space-y-2">
                  <p>{testResult.message}</p>
                  {testResult.data && (
                    <pre className="text-xs bg-white/50 p-2 rounded border overflow-auto">
                      {JSON.stringify(testResult.data, null, 2)}
                    </pre>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>1. Select a model from the dropdown above</p>
          <p>2. Configure the dynamic parameters in the form</p>
          <p>3. Click "Validate Parameters" to test parameter validation</p>
          <p>4. Click "Test Generation" to simulate video generation</p>
          <p>5. Check the results and parameter values below</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default function DynamicParamsTestPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <DynamicParamsTestContent />
    </QueryClientProvider>
  );
}
