import { ImageIcon, Sparkles, Upload, X, Zap, AlertCircle, ChevronDown, Ratio, Clock, Layers, Dices, Brush } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { useState, useRef } from "react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuthProtectedCallback } from "@/components/auth/with-auth"
import { useCreditsStore } from "@/store"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Switch } from "@/components/ui/switch"
import type { ControlPanelProps } from "../type"

export function HorizontalControlPanel({
    creativityLevel,
    setCreativityLevel,
    quality,
    setQuality,
    duration,
    setDuration,
    aspectRatio,
    setAspectRatio,
    prompt,
    setPrompt,
    negativePrompt,
    setNegativePrompt,
    steps,
    setSteps,
    seed,
    setSeed,
    images,
    handleImageUpload,
    removeImage,
    generateVideo,
    isGenerating,
    error,
    selectedModel,
    setSelectedModel,
    models,
    videoCost,
    isLoadingPresignedUrl = false,
    isLoadingModels = false
}: ControlPanelProps) {
    const { balance: creditBalance } = useCreditsStore()
    const [showInsufficientCreditsDialog, setShowInsufficientCreditsDialog] = useState(false)
    const [showNegativePrompt, setShowNegativePrompt] = useState(!!negativePrompt)
    const fileInputRef = useRef<HTMLInputElement>(null)

    // Wrap video generation with auth protection
    const handleGenerateVideo = useAuthProtectedCallback(() => {
        // Check if user has enough credits
        if (creditBalance !== null && creditBalance < videoCost) {
            setShowInsufficientCreditsDialog(true)
            return
        }

        // Continue with video generation if credits are sufficient
        generateVideo()
    }, "generate_video")

    // Wrap image upload with auth protection
    const authenticatedHandleImageUpload = useAuthProtectedCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        handleImageUpload(e)
    }, "upload_image")

    // Handle click on upload area
    const handleUploadClick = () => {
        fileInputRef.current?.click()
    }

    // Get selected model info
    const selectedModelInfo = models.find(model => model.id === selectedModel)

    // Check if the selected model allows reference images
    const allowReferenceImage = selectedModelInfo?.default_config?.allow_reference_image !== false

    return (
        <div className="relative w-full bg-background border border-border/30 rounded-xl overflow-hidden">
            <div className="p-4">
                {/* Prompt Input and Image Upload Combined Section */}
                <div className="flex space-x-4">
                    {/* Main Prompt Section - Takes more space */}
                    <div className="flex-1 flex flex-col">
                        <div className="mb-2 flex items-center">
                            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2"></div>
                            <h3 className="text-base font-medium text-foreground">Creative Description</h3>
                            <div className="text-xs text-muted-foreground ml-auto">
                                <span>{prompt.length}/500</span>
                            </div>
                        </div>

                        <textarea
                            placeholder="Describe the video you want to create..."
                            maxLength={500}
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            className="w-full h-28 px-4 py-3 text-base rounded-xl bg-card/30 border border-border/50 focus:outline-hidden placeholder:text-muted-foreground/70 resize-none"
                        />

                        {/* Negative Prompt - conditionally rendered */}
                        {showNegativePrompt && (
                            <div className="mt-3">
                                <div className="flex items-center mb-1.5">
                                    <h3 className="text-sm font-medium text-foreground">Negative Prompt</h3>
                                    <div className="text-xs text-muted-foreground ml-auto">
                                        <span>{negativePrompt.length}/300</span>
                                    </div>
                                </div>
                                <textarea
                                    placeholder="Elements you want to exclude from the video..."
                                    maxLength={300}
                                    value={negativePrompt}
                                    onChange={(e) => setNegativePrompt(e.target.value)}
                                    className="w-full h-20 px-4 py-3 text-sm rounded-xl bg-card/30 border border-border/50 focus:outline-hidden placeholder:text-muted-foreground/70 resize-none"
                                />
                            </div>
                        )}

                        {/* Error Message */}
                        {error && (
                            <div className="mt-2 text-sm text-destructive flex items-center">
                                <AlertCircle size={14} className="mr-1" />
                                {error}
                            </div>
                        )}
                    </div>

                    {/* Reference Image Section - Takes less space, conditionally rendered */}
                    {allowReferenceImage && (
                        <div className="w-36 md:w-48 flex-shrink-0">
                            <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                    <ImageIcon size={14} className="text-primary-foreground mr-1" />
                                    <h3 className="text-sm font-medium text-foreground">Reference</h3>
                                </div>
                            </div>

                            <div className="h-28 rounded-xl border border-border/50 overflow-hidden">
                                {images.length > 0 ? (
                                    <div className="relative w-full h-full">
                                        <Image
                                            src={images[0]}
                                            alt="Reference image"
                                            fill
                                            className="object-cover"
                                        />
                                        <button
                                            onClick={() => removeImage(0)}
                                            className="absolute top-2 right-2 p-1.5 bg-black/60 hover:bg-black/80 rounded-full text-white"
                                            aria-label="Remove reference image"
                                        >
                                            <X size={14} />
                                        </button>
                                        {isLoadingPresignedUrl && (
                                            <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                                                <div className="text-xs text-white bg-black/50 px-2 py-1 rounded-md flex items-center">
                                                    <div className="mr-2 animate-spin w-3 h-3 border-2 border-white/30 border-t-white rounded-full"></div>
                                                    Uploading...
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div
                                        onClick={handleUploadClick}
                                        className="w-full h-full flex flex-col items-center justify-center bg-card/10 cursor-pointer hover:bg-card/20 transition-colors"
                                    >
                                        <Upload size={20} className="text-muted-foreground mb-1" />
                                        <p className="text-xs text-muted-foreground text-center">Add reference image</p>
                                    </div>
                                )}
                            </div>
                            <input
                                ref={fileInputRef}
                                type="file"
                                id="image-upload"
                                accept="image/*"
                                onChange={authenticatedHandleImageUpload}
                                className="hidden"
                                aria-label="Upload reference image"
                                title="Upload reference image"
                            />
                        </div>
                    )}

                    {/* Generate Button and Options */}
                    <div className="w-36 md:w-40 flex-shrink-0 flex flex-col">
                        <div className="mb-2">
                            <h3 className="text-sm font-medium text-foreground">Generate</h3>
                        </div>

                        <div className="flex-1 flex flex-col justify-between">
                            {/* Model Selection Button */}
                            <Popover>
                                <PopoverTrigger asChild>
                                    <button className="w-full p-2 rounded-lg bg-card/30 border border-border/50 text-sm flex items-center mb-2 hover:bg-card/50 transition-colors">
                                        <Zap size={14} className="text-primary-foreground mr-2" />
                                        <span className="truncate flex-1 text-left">
                                            {isLoadingModels ? "Loading models..." : selectedModelInfo?.name || "Select"}
                                        </span>
                                        <ChevronDown size={14} className="ml-1 flex-shrink-0" />
                                    </button>
                                </PopoverTrigger>
                                <PopoverContent className="w-64 p-0" align="end">
                                    <div className="max-h-64 overflow-y-auto">
                                        {models
                                            .filter(model => model.type === "checkpoint")
                                            .map(model => (
                                                <div
                                                    key={model.id}
                                                    className={`p-2 flex items-start hover:bg-accent/50 cursor-pointer transition-colors ${selectedModel === model.id ? 'bg-accent' : ''
                                                        }`}
                                                    onClick={() => setSelectedModel(model.id)}
                                                >
                                                    <div className="w-10 h-10 relative rounded-md overflow-hidden flex-shrink-0">
                                                        <Image
                                                            src={model.cover_img || '/placeholder.svg?height=40&width=40&text=Model'}
                                                            alt={model.name}
                                                            fill
                                                            className="object-cover"
                                                        />
                                                    </div>
                                                    <div className="ml-2 flex-1">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-sm truncate">{model.name}</div>
                                                        </div>
                                                        <p className="text-xs text-muted-foreground mt-0.5 line-clamp-1">
                                                            {model.description || "Professional video generation model"}
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </PopoverContent>
                            </Popover>

                            {/* Generate Button */}
                            <button
                                onClick={handleGenerateVideo}
                                disabled={isGenerating || !prompt.trim() || !selectedModel || isLoadingModels}
                                className={cn(
                                    "w-full py-3 rounded-lg text-primary-foreground font-medium flex items-center justify-center transition-all",
                                    isGenerating || isLoadingModels
                                        ? "bg-primary/80 cursor-wait"
                                        : !prompt.trim() || !selectedModel
                                            ? "bg-primary/60 cursor-not-allowed"
                                            : "bg-primary hover:bg-primary/90"
                                )}
                            >
                                {isGenerating ? (
                                    <>
                                        <div className="mr-2 animate-spin w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full"></div>
                                        <span className="text-sm">Generating...</span>
                                    </>
                                ) : (
                                    <>
                                        <Sparkles className="mr-2 h-4 w-4" />
                                        <span className="text-sm">Generate Video</span>
                                    </>
                                )}
                            </button>

                            {/* Credits Cost Display */}
                            <div className="text-center mt-2 text-xs text-muted-foreground">
                                Cost: <span className="font-bold">{videoCost} Credits</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Bottom Controls - Fine-grained Options */}
                <div className="mt-4 flex items-center space-x-1">
                    {/* Negative Prompt Toggle */}
                    <div className="flex items-center mr-3">
                        <Switch
                            checked={showNegativePrompt}
                            onCheckedChange={setShowNegativePrompt}
                            id="negative-prompt-toggle"
                            className="mr-2"
                        />
                        <label htmlFor="negative-prompt-toggle" className="text-xs text-muted-foreground cursor-pointer">
                            Negative Prompt
                        </label>
                    </div>

                    {/* Aspect Ratio Popover */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <button className="px-2 py-1.5 rounded-lg bg-card/30 border border-border/50 text-xs flex items-center hover:bg-card/50 transition-colors">
                                <Ratio size={12} className="text-primary-foreground mr-1.5" />
                                <span>{aspectRatio}</span>
                                <ChevronDown size={10} className="ml-1" />
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48 p-2" align="start">
                            <h4 className="text-xs font-medium text-foreground mb-2">Aspect Ratio</h4>
                            <div className="grid grid-cols-3 gap-1">
                                <button
                                    onClick={() => setAspectRatio("16:9")}
                                    className={cn(
                                        "p-1.5 rounded-lg text-xs flex flex-col items-center justify-center",
                                        aspectRatio === "16:9"
                                            ? "bg-primary/10 text-primary-foreground"
                                            : "bg-card/30 text-muted-foreground hover:text-foreground hover:bg-card/50"
                                    )}
                                >
                                    <div className="w-6 h-3.5 bg-muted-foreground/40 mb-1 rounded"></div>
                                    16:9
                                </button>
                                <button
                                    onClick={() => setAspectRatio("9:16")}
                                    className={cn(
                                        "p-1.5 rounded-lg text-xs flex flex-col items-center justify-center",
                                        aspectRatio === "9:16"
                                            ? "bg-primary/10 text-primary-foreground"
                                            : "bg-card/30 text-muted-foreground hover:text-foreground hover:bg-card/50"
                                    )}
                                >
                                    <div className="w-3.5 h-6 bg-muted-foreground/40 mb-1 rounded"></div>
                                    9:16
                                </button>
                                <button
                                    onClick={() => setAspectRatio("1:1")}
                                    className={cn(
                                        "p-1.5 rounded-lg text-xs flex flex-col items-center justify-center",
                                        aspectRatio === "1:1"
                                            ? "bg-primary/10 text-primary-foreground"
                                            : "bg-card/30 text-muted-foreground hover:text-foreground hover:bg-card/50"
                                    )}
                                >
                                    <div className="w-4 h-4 bg-muted-foreground/40 mb-1 rounded"></div>
                                    1:1
                                </button>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Quality Popover */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <button className="px-2 py-1.5 rounded-lg bg-card/30 border border-border/50 text-xs flex items-center hover:bg-card/50 transition-colors">
                                <Layers size={12} className="text-primary-foreground mr-1.5" />
                                <span>{quality === "standard" ? "480P" : "720P"}</span>
                                <ChevronDown size={10} className="ml-1" />
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48 p-2" align="start">
                            <h4 className="text-xs font-medium text-foreground mb-2">Resolution</h4>
                            <div className="grid grid-cols-2 gap-1">
                                <button
                                    onClick={() => setQuality("standard")}
                                    className={cn(
                                        "p-1.5 rounded-lg text-xs flex items-center justify-center",
                                        quality === "standard"
                                            ? "bg-primary/10 text-primary-foreground"
                                            : "bg-card/30 text-muted-foreground hover:text-foreground hover:bg-card/50"
                                    )}
                                >
                                    480P
                                </button>
                                <button
                                    onClick={() => setQuality("high")}
                                    className={cn(
                                        "p-1.5 rounded-lg text-xs flex items-center justify-center",
                                        quality === "high"
                                            ? "bg-primary/10 text-primary-foreground"
                                            : "bg-card/30 text-muted-foreground hover:text-foreground hover:bg-card/50"
                                    )}
                                >
                                    720P
                                </button>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Duration Popover */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <button className="px-2 py-1.5 rounded-lg bg-card/30 border border-border/50 text-xs flex items-center hover:bg-card/50 transition-colors">
                                <Clock size={12} className="text-primary-foreground mr-1.5" />
                                <span>{duration}s</span>
                                <ChevronDown size={10} className="ml-1" />
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48 p-2" align="start">
                            <h4 className="text-xs font-medium text-foreground mb-2">Duration</h4>
                            <div className="grid grid-cols-1 gap-1">
                                <button
                                    onClick={() => setDuration("5")}
                                    className={cn(
                                        "p-1.5 rounded-lg text-xs flex items-center justify-center",
                                        duration === "5"
                                            ? "bg-primary/10 text-primary-foreground"
                                            : "bg-card/30 text-muted-foreground hover:text-foreground hover:bg-card/50"
                                    )}
                                >
                                    5 seconds
                                </button>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Creativity Level Popover */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <button className="px-2 py-1.5 rounded-lg bg-card/30 border border-border/50 text-xs flex items-center hover:bg-card/50 transition-colors">
                                <Brush size={12} className="text-primary-foreground mr-1.5" />
                                <span>C: {creativityLevel}</span>
                                <ChevronDown size={10} className="ml-1" />
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-64 p-3" align="start">
                            <div className="flex items-center justify-between mb-2">
                                <h4 className="text-xs font-medium text-foreground">Creativity Level</h4>
                                <span className="text-xs text-muted-foreground">{creativityLevel}</span>
                            </div>
                            <input
                                type="range"
                                min="0"
                                max="10"
                                value={creativityLevel}
                                onChange={(e) => setCreativityLevel(parseInt(e.target.value))}
                                className="w-full h-1.5 bg-muted rounded-full appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary"
                            />
                            <div className="flex justify-between text-xs text-muted-foreground mt-1">
                                <span>More Accurate</span>
                                <span>More Creative</span>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Sampling Steps Popover */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <button className="px-2 py-1.5 rounded-lg bg-card/30 border border-border/50 text-xs flex items-center hover:bg-card/50 transition-colors">
                                <Layers size={12} className="text-primary-foreground mr-1.5" />
                                <span>S: {steps}</span>
                                <ChevronDown size={10} className="ml-1" />
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-64 p-3" align="start">
                            <div className="flex items-center justify-between mb-2">
                                <h4 className="text-xs font-medium text-foreground">Sampling Steps</h4>
                                <span className="text-xs text-muted-foreground">{steps}</span>
                            </div>
                            <input
                                type="range"
                                min="20"
                                max="60"
                                step="1"
                                value={steps}
                                onChange={(e) => setSteps(parseInt(e.target.value))}
                                className="w-full h-1.5 bg-muted rounded-full appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary"
                            />
                            <div className="flex justify-between text-xs text-muted-foreground mt-1">
                                <span>Faster</span>
                                <span>Higher Quality</span>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Seed Popover */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <button className="px-2 py-1.5 rounded-lg bg-card/30 border border-border/50 text-xs flex items-center hover:bg-card/50 transition-colors">
                                <Dices size={12} className="text-primary-foreground mr-1.5" />
                                <span>Seed</span>
                                <ChevronDown size={10} className="ml-1" />
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-56 p-3" align="start">
                            <label className="block text-xs font-medium text-foreground mb-2">Random Seed</label>
                            <div className="flex gap-1">
                                <input
                                    type="text"
                                    placeholder="Random seed"
                                    value={seed}
                                    onChange={(e) => setSeed(e.target.value)}
                                    className="flex-1 px-2 py-1.5 text-xs rounded-lg bg-card/30 border border-border/50 focus:border-primary focus:ring-1 focus:ring-primary"
                                />
                                <button
                                    onClick={() => setSeed(Math.floor(Math.random() * 1000000).toString())}
                                    className="bg-muted hover:bg-muted/80 px-2 py-1.5 rounded-md transition-colors"
                                    title="Generate random seed"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                        <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                                        <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                                        <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                                        <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                                        <line x1="12" y1="22.08" x2="12" y2="12"></line>
                                    </svg>
                                </button>
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">Same seed will produce similar results</p>
                        </PopoverContent>
                    </Popover>
                </div>
            </div>

            {/* Insufficient Credits Dialog */}
            <Dialog open={showInsufficientCreditsDialog} onOpenChange={setShowInsufficientCreditsDialog}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle className="flex items-center">
                            <AlertCircle className="h-5 w-5 text-destructive mr-2" />
                            Insufficient Credits
                        </DialogTitle>
                        <DialogDescription>
                            You don't have enough credits to generate this video. You need {videoCost} credits, but you only have {creditBalance} credits.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex flex-col sm:flex-row gap-2">
                        <Button variant="outline" onClick={() => setShowInsufficientCreditsDialog(false)}>
                            Cancel
                        </Button>
                        <Link href="/membership">
                            <Button className="w-full">
                                Purchase Credits
                            </Button>
                        </Link>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
} 