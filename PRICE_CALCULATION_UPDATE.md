# 视频生成价格计算策略更新

## 概述

本次更新将视频生成的价格计算策略从固定的5秒/10秒模式改为支持1秒步长的精确计算，以5秒为基准价格，支持6、7、8、9等任意秒数的价格计算。

## 修改内容

### 1. 后端修改

#### 1.1 扩展VideoDuration枚举 (`src/common/constants/video.ts`)
```typescript
export enum VideoDuration {
    SHORT = '5',
    DURATION_6 = '6',
    DURATION_7 = '7',
    MEDIUM = '8',
    DURATION_9 = '9',
    LONG = '10',
}
```

#### 1.2 重构价格计算逻辑 (`src/generation/generation.service.ts`)
- **旧逻辑**: 只支持5秒和10秒，10秒价格固定为5秒的2倍
- **新逻辑**: 基于5秒基准价格计算每秒单价，支持任意秒数的精确计算

**新的计算公式**:
```
每秒价格 = 基础价格(5秒) ÷ 5
实际价格 = Math.ceil(每秒价格 × 实际时长)
```

**特性**:
- 支持任意正整数秒数
- 使用向上取整确保不出现小数积分
- 包含详细的调试日志
- 完善的错误处理和边界情况处理

### 2. 前端修改

#### 2.1 更新类型定义 (`types/video.ts`)
```typescript
export type Duration = "5" | "6" | "7" | "8" | "9" | "10"
export type APIDuration = "5" | "6" | "7" | "8" | "9" | "10"
```

#### 2.2 扩展时长选项常量 (`app/create/components/ControlPanel/ControlPanelContext.tsx`)
```typescript
export const DURATION_OPTIONS = {
  SHORT: "5",
  DURATION_6: "6",
  DURATION_7: "7",
  MEDIUM: "8",
  DURATION_9: "9",
  LONG: "10",
};
```

#### 2.3 更新视频生成器hook (`hooks/use-video-generator.ts`)
- 支持解析5s、6s、7s、8s、9s、10s等时长格式
- 增强了时长值的验证和处理

## 价格计算示例

### 基础价格为50积分的模型:
- 5秒: 50积分 (基准价格)
- 6秒: 60积分 (50÷5×6=60)
- 7秒: 70积分 (50÷5×7=70)
- 8秒: 80积分 (50÷5×8=80)
- 9秒: 90积分 (50÷5×9=90)
- 10秒: 100积分 (50÷5×10=100)

### 基础价格为80积分的模型:
- 5秒: 80积分 (基准价格)
- 8秒: 128积分 (80÷5×8=128)

### 非整除情况 (基础价格45积分):
- 6秒: 54积分 (Math.ceil(45÷5×6)=54)

## 向后兼容性

- ✅ 完全向后兼容现有的5秒和10秒时长
- ✅ 现有API接口无需修改
- ✅ 现有数据库记录无需迁移
- ✅ 前端UI可以继续使用现有的时长选择器

## 边界情况处理

- **无效时长** (0、负数、非数字): 自动使用基础价格(5秒价格)
- **极小时长** (1秒): 正常计算，价格为基础价格的1/5
- **极大时长** (100秒): 正常计算，但可能需要大量积分

## 测试验证

已通过完整的单元测试验证:
- ✅ 9个标准测试用例全部通过
- ✅ 6个边界情况测试全部通过
- ✅ 成功率: 100%

## 部署注意事项

1. **无需数据库迁移**: 所有修改都是代码层面的逻辑更新
2. **无需停机**: 可以热部署，不影响现有功能
3. **渐进式启用**: 新的时长选项可以通过模型配置逐步启用
4. **监控建议**: 部署后监控价格计算的准确性和性能

## 未来扩展

- 可以通过模型配置表动态控制每个模型支持的时长范围
- 可以为不同模型设置不同的价格计算策略
- 可以添加批量折扣或会员折扣等高级定价功能

## 相关文件

### 后端文件
- `src/common/constants/video.ts` - 视频常量定义
- `src/generation/generation.service.ts` - 价格计算逻辑
- `src/generation/dto/task-price.dto.ts` - 价格查询DTO

### 前端文件
- `types/video.ts` - 类型定义
- `app/create/components/ControlPanel/ControlPanelContext.tsx` - 控制面板常量
- `hooks/use-video-generator.ts` - 视频生成器hook
