import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
    // 获取用户代理
    const userAgent = request.headers.get('user-agent') || ''

    // 检查是否是搜索引擎爬虫
    const isBot = /bot|crawler|spider|crawling/i.test(userAgent)

    // 检查是否是视频详情页面
    const isVideoDetailPage = request.nextUrl.pathname.startsWith('/posts/')

    // 如果是爬虫访问视频详情页，让它正常访问以支持SEO
    if (isBot && isVideoDetailPage) {
        return NextResponse.next()
    }

    // 检查是否存在设备宽度的 cookie
    const clientWidth = request.cookies.get('client-width')?.value

    // 创建响应对象
    const response = NextResponse.next()

    // 如果没有宽度 cookie 但有 viewport-width 头信息，设置 cookie
    const viewportWidth = request.headers.get('viewport-width')
    if (!clientWidth && viewportWidth) {
        response.cookies.set('client-width', viewportWidth, {
            maxAge: 60 * 60 * 24 * 7, // 7 天
            path: '/',
            sameSite: 'strict',
        })
    }

    // 如果有宽度信息，添加到 x-client-hints 头信息中
    if (clientWidth || viewportWidth) {
        const width = parseInt(clientWidth || viewportWidth || '0', 10)
        response.headers.set('x-client-hints', JSON.stringify({ width }))
    }

    // 对于普通用户，我们也允许直接访问视频详情页
    // 客户端代码会处理是否显示弹窗或正常页面
    return response
}

// 配置中间件应用的路径
export const config = {
    matcher: ['/posts/:path*'],
} 