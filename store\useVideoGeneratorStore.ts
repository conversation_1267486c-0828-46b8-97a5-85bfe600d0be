import { create } from 'zustand';
import { generationApi } from '@/lib/api/generation';
import type { VideoGenerationRequest } from '@/lib/api/generation';
import type { APIDuration, APIRatio } from '@/types/video';
import { Model } from '@/types/model';

interface VideoGeneratorState {
    // 基本参数
    generationType: string;
    creativityLevel: number; // 改为数值类型，范围0-1
    quality: string;
    duration: string;
    aspectRatio: string;
    prompt: string;
    negativePrompt: string;
    steps: number;
    seed: number;
    images: string[];
    // 新增效果ID
    selectedEffectName: string;

    // 生成状态
    isGenerating: boolean;
    error: string | null;
    currentTask: any | null;
    isLoadingPresignedUrl: boolean;
    // 新任务生成标志，用于通知历史面板刷新
    hasNewTask: boolean;

    // 操作方法
    setGenerationType: (type: string) => void;
    setCreativityLevel: (level: number) => void; // 改为数值类型
    setQuality: (quality: string) => void;
    setDuration: (duration: string) => void;
    setAspectRatio: (ratio: string) => void;
    setPrompt: (prompt: string) => void;
    setNegativePrompt: (prompt: string) => void;
    setSteps: (steps: number) => void;
    setSeed: (seed: number) => void;
    // 新增设置效果方法
    setSelectedEffectName: (effectName: string) => void;
    // 重置新任务标志
    resetHasNewTask: () => void;

    // 图片相关
    addImage: (image: string) => void;
    appendImage: (image: string) => void;
    removeImage: (index: number) => void;

    // 视频生成
    generateVideo: (modelId: Model, selectedEffect?: any, dynamicParams?: Record<string, any>) => Promise<any>;

    // 加载任务参数
    loadTaskParams: (taskId: string) => Promise<any>;

    // 设置首尾帧图片（用于remix功能）
    setStartToEndImages: (startImageUrl: string, endImageUrl: string) => void;

    // 重置状态
    resetState: () => void;
}

const initialState = {
    generationType: 'text-to-video',
    creativityLevel: 0.5, // 默认中等创造力，范围0-1
    quality: 'standard',
    duration: '5',
    aspectRatio: '9:16',
    prompt: '',
    negativePrompt: '',
    steps: 25,
    seed: -1,
    images: [],
    selectedEffectName: '', // 新增效果ID初始值
    isGenerating: false,
    error: null,
    currentTask: null,
    isLoadingPresignedUrl: false,
    hasNewTask: false,
};

const useVideoGeneratorStore = create<VideoGeneratorState>((set, get) => ({
    ...initialState,

    // 设置方法
    setGenerationType: (type) => set({ generationType: type }),
    setCreativityLevel: (level) => set({ creativityLevel: level }),
    setQuality: (quality) => set({ quality }),
    setDuration: (duration) => set({ duration }),
    setAspectRatio: (ratio) => set({ aspectRatio: ratio }),
    setPrompt: (prompt) => set({ prompt }),
    setNegativePrompt: (prompt) => set({ negativePrompt: prompt }),
    setSteps: (steps) => set({ steps }),
    setSeed: (seed) => set({ seed }),
    // 新增设置效果方法
    setSelectedEffectName: (effectName) => set({ selectedEffectName: effectName }),
    resetHasNewTask: () => set({ hasNewTask: false }),

    // 媒体处理（图片或视频）
    addImage: (image) => set((state) => {
        // 保持当前生成类型不变，如果已经设置为video-to-video
        const currentType = state.generationType;
        const newType = currentType === 'video-to-video' ? 'video-to-video' : 'image-to-video';

        return {
            // 只保留新添加的图片，不追加到现有图片列表
            images: [image],
            // 如果添加媒体，根据当前类型决定模式
            generationType: newType
        };
    }),

    // 追加图片（保留原有功能，但不在主要流程中使用）
    appendImage: (image) => set((state) => {
        // 保持当前生成类型不变，如果已经设置为video-to-video
        const currentType = state.generationType;
        const newType = currentType === 'video-to-video' ? 'video-to-video' : 'image-to-video';

        return {
            images: [...state.images, image],
            // 如果添加媒体，根据当前类型决定模式
            generationType: newType
        };
    }),

    removeImage: (index) => set((state) => {
        const newImages = [...state.images];
        newImages.splice(index, 1);

        // 如果没有媒体了，切换回文本到视频模式
        const newType = newImages.length === 0 ? 'text-to-video' : state.generationType;

        return {
            images: newImages,
            generationType: newType
        };
    }),

    // 生成视频
    generateVideo: async (model: Model, selectedEffect, dynamicParams = {}) => {
        const state = get();

        // 基本验证
        if (state.generationType === 'text-to-video' && !state.prompt) {
            set({ error: "Please enter a prompt" });
            return;
        }

        if (state.generationType === 'image-to-video' && state.images.length === 0) {
            set({ error: "Please upload at least one image" });
            return;
        }

        if (state.generationType === 'video-to-video' && state.images.length === 0) {
            set({ error: "Please upload a reference video" });
            return;
        }

        try {
            set({ isGenerating: true, error: null });


            // 准备参数 - 根据API实际要求调整参数名
            const params: VideoGenerationRequest = {
                model_id: model.id,
                prompt: state.prompt,
                negative_prompt: state.negativePrompt,
                steps: state.steps,
                seed: state.seed,
                gen_type: model.model_type,
                // 直接使用creativityLevel作为guidance_scale (已经是0-1范围)
                guidance_scale: state.creativityLevel,
                // 转换期限为API格式
                duration: state.duration as APIDuration,
                // 转换画质为API格式
                // definition: state.quality === 'high' ? '720P' : '480P' as APIDefinition,
                // 转换比例为API格式
                ratio: state.aspectRatio as APIRatio,
                // 添加动态参数
                dynamic_params: Object.keys(dynamicParams).length > 0 ? dynamicParams : undefined,
            };

            // 如果选择了效果，添加效果名称到参数
            if (state.selectedEffectName) {
                params.effect_name = state.selectedEffectName;

                // 如果传入了效果对象，且有trigger_words，添加到请求参数
                if (selectedEffect && selectedEffect.trigger_words && selectedEffect.trigger_words.length > 0) {
                    params.trigger_words = selectedEffect.trigger_words;
                }
            }

            // 检查是否为首尾帧模型
            const isStartToEndModel = model.default_config?.start_to_end === true;

            if (isStartToEndModel) {
                // 首尾帧模型需要从外部传入start_image_url和end_image_url
                // 这些参数应该在调用generateVideo时传入
                // 暂时保持原有逻辑，具体实现在ControlPanelContext中处理
            } else if (model.model_type === 'image-to-video') {
                params.refer_img_url = state.images[0];
            } else if (model.model_type === 'video-to-video') {
                params.video_url = state.images[0];
            }

            // 调用API创建任务
            const task = await generationApi.createTask(params);

            // 更新状态
            set({
                currentTask: task,
                isGenerating: false,
                hasNewTask: true // 设置新任务标志
            });

            return task;
        } catch (error) {
            console.error("视频生成失败:", error);
            set({
                error: error instanceof Error ? error.message : "Failed to generate video",
                isGenerating: false
            });
        }
    },

    // 加载任务参数
    loadTaskParams: async (taskId) => {
        try {
            set({ isLoadingPresignedUrl: true });
            const task = await generationApi.getTaskDetail(taskId);

            if (task && task.input_params) {
                const params = task.input_params;

                // 确定生成类型
                let generationType = 'text-to-video';
                // @ts-ignore
                // 兼容老的定义
                if (params.gen_type === 'img2video' || params.gen_type === 'image-to-video') {
                    generationType = 'image-to-video';
                    // @ts-ignore
                    // 兼容老的定义
                } else if (params.gen_type === 'video2video' || params.gen_type === 'video-to-video') {
                    generationType = 'video-to-video';
                }

                // 确定媒体URL
                let mediaUrl = '';
                if (params.refer_img_url) {
                    mediaUrl = params.refer_img_url;
                } else if (params.video_url) {
                    mediaUrl = params.video_url;
                }

                // 处理首尾帧图片（如果存在）
                let startImageUrl = '';
                let endImageUrl = '';
                // @ts-ignore - params可能包含start_image_url和end_image_url字段
                if (params.start_image_url) {
                    // @ts-ignore
                    startImageUrl = params.start_image_url;
                }
                // @ts-ignore - params可能包含start_image_url和end_image_url字段
                if (params.end_image_url) {
                    // @ts-ignore
                    endImageUrl = params.end_image_url;
                }

                // 将API参数映射到UI状态
                set({
                    prompt: params.prompt || '',
                    negativePrompt: params.negative_prompt || '',
                    steps: params.steps || 25,
                    seed: params.seed || -1,
                    // 去掉duration末尾的's'
                    duration: params.duration ? params.duration.replace('s', '') : '5',
                    // 设置质量 (definition -> quality)
                    quality: params.definition === '720P' ? 'high' : 'standard',
                    // 设置比例
                    aspectRatio: params.ratio || '16:9',
                    // 直接使用guidance_scale作为creativityLevel (已经是0-1范围)
                    creativityLevel: params.guidance_scale || 0.5,
                    // 设置生成类型
                    generationType: generationType,
                    // 媒体列表 - API中可能是单个媒体URL
                    images: mediaUrl ? [mediaUrl] : [],
                    // 设置效果ID
                    selectedEffectName: params.effect_name || '',
                });

                // 如果有首尾帧图片，设置它们
                if (startImageUrl && endImageUrl) {
                    // 使用get()获取当前实例并调用方法
                    get().setStartToEndImages(startImageUrl, endImageUrl);
                }
            }

            set({ isLoadingPresignedUrl: false });
            return task;
        } catch (error) {
            console.error("加载任务参数失败:", error);
            set({
                error: error instanceof Error ? error.message : "Failed to load task parameters",
                isLoadingPresignedUrl: false
            });
            return null;
        }
    },

    // 设置首尾帧图片（用于remix功能）
    setStartToEndImages: (startImageUrl: string, endImageUrl: string) => {
        // 这个方法会触发一个自定义事件，让ControlPanelContext监听并设置图片
        window.dispatchEvent(new CustomEvent('setStartToEndImages', {
            detail: { startImageUrl, endImageUrl }
        }));
    },

    // 重置状态
    resetState: () => set(initialState),
}));

export default useVideoGeneratorStore;