import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CustomLogger } from '../services/logger.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
    private readonly apiKey: string;
    private guardLogger: CustomLogger;

    constructor(
        private readonly configService: ConfigService,
        private readonly logger: CustomLogger,
    ) {
        this.apiKey = this.configService.get<string>('API_KEY') || 'demo_api_key';
        this.guardLogger = this.logger.createLoggerWithContext(ApiKeyGuard.name);
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const apiKey = this.extractApiKey(request);

        if (!apiKey || apiKey !== this.apiKey) {
            this.guardLogger.warn(`无效的API密钥: ${apiKey}`);
            throw new UnauthorizedException('无效的API密钥');
        }

        return true;
    }

    private extractApiKey(request: any): string | null {
        // 首先尝试从请求头中获取
        const apiKey = request.headers['x-api-key'];
        if (apiKey) {
            return apiKey;
        }

        // 然后尝试从查询参数中获取
        if (request.query && request.query.api_key) {
            return request.query.api_key;
        }

        // 最后尝试从请求体中获取
        if (request.body && request.body.api_key) {
            return request.body.api_key;
        }

        return null;
    }
} 