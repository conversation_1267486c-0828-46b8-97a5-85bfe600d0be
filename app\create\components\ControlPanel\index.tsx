"use client"

import React, { ReactNode } from 'react';
import { ControlPanelProvider } from './ControlPanelContext';
import { ModelTabContent } from './ModelTabContent';
import { ReferenceImageUploader } from './ReferenceImageUploader';
import { StartToEndImageUploader } from './StartToEndImageUploader';
import { SettingsPills } from './SettingsPills';
import { GenerateButton } from './GenerateButton';
import { PromptInput } from '../prompt-input';
import { useControlPanel } from './ControlPanelContext';
import { CreatePageCouponBanner } from '@/components/coupon/create-page-coupon-banner';
import { DynamicModelParams } from '@/components/ui/dynamic-model-params';

interface ControlPanelContentProps {
  extraControls?: ReactNode;
}

// Inner component that uses the context
const ControlPanelContent: React.FC<ControlPanelContentProps> = ({ extraControls }) => {
  const {
    error,
    globalPrompt,
    debouncedSetPrompt,
    isStartToEndModel,
    hasAdvancedConfig,
    selectedModelInfo,
    dynamicParams,
    setDynamicParams,
    dynamicParamErrors
  } = useControlPanel();

  return (
    <div className="flex flex-col md:w-[40%] w-full mx-auto md:px-2 space-y-4 max-h-screen overflow-auto">
      {/* Model and Effect Tabs with extraControls for mobile */}
      <div className="flex flex-col">
        <ModelTabContent extraControls={extraControls} />
      </div>

      {/* Reference Image Uploader */}
      <div className="px-2 md:px-0">
        {isStartToEndModel ? (
          <StartToEndImageUploader />
        ) : (
          <ReferenceImageUploader />
        )}
      </div>

      {/* Prompt Input */}
      <PromptInput
        initialPrompt={globalPrompt}
        onPromptChange={debouncedSetPrompt}
        errorMessage={error}
      />

      {/* Settings Pills */}
      <div className="px-2 md:px-0">
        <SettingsPills />
      </div>

      {/* Dynamic Model Parameters */}
      {hasAdvancedConfig && selectedModelInfo && (
        <div className="px-2 md:px-0">
          <DynamicModelParams
            modelId={selectedModelInfo.storage_path || ''}
            values={dynamicParams}
            onChange={setDynamicParams}
            errors={dynamicParamErrors}
          />
        </div>
      )}

      {/* Coupon Banner */}
      <div className="px-2 md:px-0">
        <CreatePageCouponBanner />
      </div>

      {/* Generate Button */}
      <div className="px-2 md:px-0">
        <GenerateButton />
      </div>
    </div>
  );
};

interface ControlPanelProps {
  extraControls?: ReactNode;
}

// Main component that provides the context
export function ControlPanel({ extraControls }: ControlPanelProps) {
  return (
    <ControlPanelProvider>
      <ControlPanelContent extraControls={extraControls} />
    </ControlPanelProvider>
  );
}
