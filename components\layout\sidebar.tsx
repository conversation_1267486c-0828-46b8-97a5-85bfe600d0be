"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { SidebarCouponBanner } from "@/components/coupon/sidebar-coupon-banner";
import {
  Compass,
  Box,
  Bookmark,
  Sparkles,
  Code,
  Film,
  Brain,
  BookOpen,
  MoreHorizontal,
  X,
  Blocks,
} from "lucide-react";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<any>;
  cyberpunk?: boolean;
  badge?: {
    text: string;
    colorClass: string;
  };
}

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname() || "";
  const isCreatePage = pathname.startsWith("/create");
  const isTrainPage = pathname.startsWith("/train");
  const isLegoPage = pathname.startsWith("/lego");
  const shouldCollapse = isCreatePage || isTrainPage || isLegoPage;
  const [isCollapsed, setIsCollapsed] = useState(shouldCollapse);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // 确保只在客户端渲染动态内容
  useEffect(() => {
    // create和train页面始终处于缩小状态，其他页面始终展开
    if (shouldCollapse) {
      setIsCollapsed(true);
    } else {
      setIsCollapsed(false);
    }
  }, [shouldCollapse]);

  // 主要导航项（桌面端显示的所有项目）
  const mainNavItems: NavItem[] = [
    { href: "/", label: "Explore", icon: Compass },
    { href: "/create", label: "Create", icon: Sparkles },
    // { href: "/favorite", label: "Favorite", icon: Bookmark },
    {
      href: "/lego",
      label: "Lego Pixel",
      icon: Blocks,
      badge: {
        text: "Free",
        colorClass: "bg-gradient-to-r from-green-500 to-emerald-500",
      },
    },
    // { href: "/models", label: "Video Models", icon: Box },
    { href: "/train", label: "Video Fusion", icon: Brain, cyberpunk: true },
    {
      href: "/agents",
      label: "Movie Agents",
      icon: Film,
      // badge: {
      //   text: "Bonus",
      //   colorClass: "bg-gradient-to-r from-amber-500 to-orange-500",
      // },
    },
  ];

  // 底部导航项（桌面端底部显示的项目）
  const bottomNavItems: NavItem[] = [
    { href: "/blog", label: "Blog", icon: BookOpen },
    {
      href: "/apis",
      label: "API Platform",
      icon: Code,
      badge: {
        text: "New",
        colorClass: "bg-gradient-to-r from-purple-500 to-pink-500",
      },
    },
  ];

  // 移动端主导航项
  const mobileMainNavItems: NavItem[] = [
    { href: "/", label: "Explore", icon: Compass },
    { href: "/create", label: "Create", icon: Sparkles },
    {
      href: "/lego",
      label: "Lego",
      icon: Blocks,
      badge: {
        text: "Free",
        colorClass: "bg-gradient-to-r from-green-500 to-emerald-500",
      },
    },
    {
      href: "/agents",
      label: "Movie Agents",
      icon: Film,
      badge: {
        text: "Bonus",
        colorClass: "bg-gradient-to-r from-amber-500 to-orange-500",
      },
    },
  ];

  // 移动端More菜单项
  const mobileMoreItems: NavItem[] = [
    { href: "/train", label: "Video Fusion", icon: Brain, cyberpunk: true },
    { href: "/blog", label: "Blog", icon: BookOpen },
    {
      href: "/apis",
      label: "API Platform",
      icon: Code,
      badge: {
        text: "New",
        colorClass: "bg-gradient-to-r from-purple-500 to-pink-500",
      },
    },
  ];

  // Mobile Navigation Bar
  const MobileNavBar = () => (
    <>
      <div className="fixed bottom-0 left-0 right-0 h-16 bg-background/95 backdrop-blur-sm border-t border-border flex items-center justify-around z-50 lg:hidden safe-area-bottom pb-safe">
        {mobileMainNavItems.map((item) => {
          const isActive =
            pathname === item.href ||
            (item.href !== "/" && pathname.startsWith(item.href));
          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center w-16 h-full py-1 relative group",
                isActive ? "text-foreground" : "text-muted-foreground"
              )}
            >
              <Icon
                className={cn(
                  "h-5 w-5 mb-1 transition-all duration-300",
                  isActive ? "text-foreground" : "text-muted-foreground"
                )}
              />
              <span className="text-[10px] font-medium">{item.label}</span>
              {item.badge && (
                <span
                  className={cn(
                    "absolute -top-1 -right-1 px-1.5 py-0.5 text-[8px] font-medium rounded-full text-white",
                    item.badge.colorClass
                  )}
                >
                  {item.badge.text}
                </span>
              )}
            </Link>
          );
        })}

        {/* More Button */}
        <button
          onClick={() => setShowMobileMenu(true)}
          className={cn(
            "flex flex-col items-center justify-center w-16 h-full py-1 relative group",
            showMobileMenu ? "text-foreground" : "text-muted-foreground"
          )}
        >
          <MoreHorizontal className="h-5 w-5 mb-1" />
          <span className="text-[10px] font-medium">More</span>
        </button>
      </div>

      {/* Mobile More Menu */}
      <AnimatePresence>
        {showMobileMenu && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 lg:hidden"
              onClick={() => setShowMobileMenu(false)}
            />
            <motion.div
              initial={{ y: "100%" }}
              animate={{ y: 0 }}
              exit={{ y: "100%" }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              className="fixed bottom-0 left-0 right-0 bg-background border-t border-border rounded-t-2xl z-50 lg:hidden"
            >
              <div className="flex justify-between items-center px-4 py-4 border-b border-border">
                <h2 className="text-lg font-semibold">More</h2>
                <button
                  onClick={() => setShowMobileMenu(false)}
                  className="p-2 hover:bg-muted rounded-full"
                  title="Close menu"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="px-2 py-4">
                {mobileMoreItems.map((item) => {
                  const isActive =
                    pathname === item.href ||
                    (item.href !== "/" && pathname.startsWith(item.href));
                  const Icon = item.icon;
                  const isCyberpunk = item.cyberpunk;

                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setShowMobileMenu(false)}
                      className={cn(
                        "flex items-center px-4 py-3 rounded-lg mb-1 relative",
                        isCyberpunk && isActive
                          ? "bg-black text-[#F5EFFF] border border-[#F5EFFF]/50"
                          : isActive
                            ? "bg-muted"
                            : "hover:bg-muted/50"
                      )}
                    >
                      <Icon
                        className={cn(
                          "h-5 w-5 mr-3",
                          isCyberpunk && isActive
                            ? "text-[#F5EFFF]"
                            : "text-muted-foreground"
                        )}
                      />
                      <span className="font-medium">{item.label}</span>
                      {item.badge && (
                        <span
                          className={cn(
                            "ml-auto px-2 py-0.5 text-[10px] font-medium rounded-full text-white",
                            item.badge.colorClass
                          )}
                        >
                          {item.badge.text}
                        </span>
                      )}
                    </Link>
                  );
                })}
              </div>
              <div className="px-4 py-4 border-t border-border">
                {renderSocialLinks()}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );

  // 导航项渲染函数
  const renderNavItem = (item: NavItem) => {
    const isActive =
      pathname === item.href ||
      (item.href !== "/" && pathname.startsWith(item.href));
    const Icon = item.icon;
    const isCyberpunk = item.cyberpunk;
    const hasBadge = item.badge;

    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "flex items-center text-left rounded-md transition-colors relative group",
          isActive && isCyberpunk
            ? "bg-black border border-[#F5EFFF]/50 text-[#F5EFFF] font-medium shadow-[0_0_8px_rgba(0,255,0,0.5)]"
            : isActive
              ? "bg-gray-800 text-white font-medium"
              : isCyberpunk
                ? "text-muted-foreground hover:text-[#F5EFFF] hover:bg-black/40 hover:border hover:border-[#F5EFFF]/30 hover:shadow-[0_0_5px_rgba(0,255,0,0.3)]"
                : pathname.startsWith("/train") && hasBadge
                  ? "bg-black/40 border border-[#F5EFFF]/20 text-white/80 hover:text-white hover:bg-black/60 hover:border-[#F5EFFF]/40 hover:shadow-[0_0_5px_rgba(0,255,0,0.2)]"
                  : "text-muted-foreground hover:bg-muted hover:text-foreground",
          isCollapsed ? "justify-center py-3 px-1 w-full" : "px-3 py-2.5 w-full"
        )}
      >
        <Icon
          className={cn(
            "h-5 w-5 transition-all duration-300",
            isCollapsed ? "mr-0" : "mr-3",
            isActive && isCyberpunk
              ? "text-[#F5EFFF]"
              : isActive
                ? "text-white"
                : isCyberpunk
                  ? "text-muted-foreground group-hover:text-[#F5EFFF]"
                  : pathname.startsWith("/train") && hasBadge
                    ? "text-[#F5EFFF]/70"
                    : "text-muted-foreground"
          )}
        />

        {!isCollapsed &&
          (isCyberpunk ? (
            <span
              className={cn(
                "relative transition-all duration-300",
                isActive ? "glitch-text" : "group-hover:glitch-text"
              )}
              data-text={item.label}
            >
              {item.label}
            </span>
          ) : (
            <span
              className={
                hasBadge && pathname.startsWith("/train")
                  ? "text-white/90 font-medium"
                  : ""
              }
            >
              {item.label}
            </span>
          ))}

        {/* 网络标签 - 统一处理徽章 */}
        {isCyberpunk && !isCollapsed && (
          <span
            className={cn(
              "absolute -top-1 -right-1 px-2 py-0.5 text-[10px] font-mono rounded-sm bg-black border border-[#F5EFFF]/50 text-[#F5EFFF] transition-all duration-300",
              !isActive && "opacity-0 group-hover:opacity-100"
            )}
          >
            AI
          </span>
        )}

        {hasBadge && !isCollapsed && (
          <span
            className={cn(
              "ml-2 px-2 py-0.5 text-[10px] font-medium rounded-full text-white",
              pathname.startsWith("/train")
                ? "bg-black border border-[#F5EFFF]/30 text-[#F5EFFF]"
                : hasBadge.colorClass
            )}
          >
            {hasBadge.text}
          </span>
        )}

        {/* 为收缩模式添加popover tooltip */}
        {isCollapsed && (
          <div className="absolute left-0 top-0 w-full h-full">
            <div className="absolute left-[calc(100%+8px)] px-2.5 py-1.5 rounded-md bg-background border border-border shadow-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform -translate-y-1/2 top-1/2 z-[9999] whitespace-nowrap">
              <span className="text-sm text-foreground font-medium">
                {item.label}
              </span>
              {hasBadge && (
                <span
                  className={cn(
                    "ml-1.5 px-1 py-0.5 text-[8px] font-medium rounded-sm text-white inline-block",
                    hasBadge.colorClass
                  )}
                >
                  {hasBadge.text}
                </span>
              )}
            </div>
          </div>
        )}
      </Link>
    );
  };

  const renderSocialLinks = () => {
    return (
      <div className="flex justify-center space-x-4 mb-4">
        <a
          href="https://x.com/reelmind_ai"
          target="_blank"
          rel="noopener noreferrer"
          className="text-muted-foreground hover:text-foreground transition-colors"
          aria-label="Follow us on X (Twitter)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M4 4l11.733 16h4.267l-11.733 -16z" />
            <path d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772" />
          </svg>
        </a>
        <a
          href="https://discord.gg/hEBaDtpqd7"
          target="_blank"
          rel="noopener noreferrer"
          className="text-muted-foreground hover:text-foreground transition-colors"
          aria-label="Join our Discord server"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="9" cy="12" r="1" />
            <circle cx="15" cy="12" r="1" />
            <path d="M7.5 7.5c3.5-1 5.5-1 9 0" />
            <path d="M7 16.5c3.5 1 6.5 1 10 0" />
            <path d="M15.5 17c0 1 1.5 3 2 3 1.5 0 2.833-1.667 3.5-3 .667-1.667.5-5.833-1.5-11.5-1.457-1.015-3-1.34-4.5-1.5l-1 2.5" />
            <path d="M8.5 17c0 1-1.356 3-1.832 3-1.429 0-2.698-1.667-3.333-3-.635-1.667-.476-5.833 1.428-11.5C6.151 4.485 7.545 4.16 9 4l1 2.5" />
          </svg>
        </a>
        <a
          href="https://www.tiktok.com/@reelmind.ai"
          target="_blank"
          rel="noopener noreferrer"
          className="text-muted-foreground hover:text-foreground transition-colors"
          aria-label="Follow us on TikTok"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M9 12a4 4 0 1 0 4 4V4a5 5 0 0 0 5 5" />
          </svg>
        </a>
        <a
          href="https://www.youtube.com/@reelmind_ai"
          target="_blank"
          rel="noopener noreferrer"
          className="text-muted-foreground hover:text-foreground transition-colors"
          aria-label="Subscribe to our YouTube channel"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17" />
            <path d="m10 15 5-3-5-3z" />
          </svg>
        </a>
      </div>
    );
  };

  return (
    <>
      {/* 桌面侧边栏 */}
      <motion.aside
        initial={false}
        animate={{
          width: isCollapsed ? 52 : 256,
          transition: { type: "spring", damping: 20, stiffness: 200 },
        }}
        className={cn(
          "hidden lg:flex h-full flex-shrink-0 flex-col overflow-hidden pb-6",
          pathname.startsWith("/train")
            ? "bg-black backdrop-blur-sm"
            : "bg-background/95 backdrop-blur-sm supports-backdrop-filter:bg-background/60",
          className
        )}
      >
        {pathname.startsWith("/train") && (
          <>
            {/* 为train页面添加赛博朋克风格背景 */}
            <div className="absolute inset-0 pointer-events-none z-0">
              {/* 垂直网格线 */}
              <div
                className="absolute inset-0
                bg-[linear-gradient(90deg,transparent_23px,#F5EFFF_24px,#F5EFFF_24.5px,transparent_25px)]
                bg-[length:30px_100%] opacity-[0.05]"
              ></div>

              {/* 微小点状背景 */}
              <div
                className="absolute w-full h-full
                bg-[radial-gradient(#F5EFFF_0.3px,transparent_0.3px)]
                bg-[size:8px_8px] opacity-[0.02]"
              ></div>

              {/* 边缘渐变 */}
              <div className="absolute top-0 bottom-0 right-0 w-1 bg-gradient-to-r from-transparent to-[#F5EFFF]/20"></div>
            </div>
          </>
        )}

        {/* 主导航菜单 */}
        <nav
          className={cn(
            "space-y-2 text-base overflow-y-auto overflow-x-hidden",
            isCollapsed ? "px-2" : "p-3 pt-0"
          )}
        >
          {mainNavItems.map(renderNavItem)}
        </nav>

        {/* 优惠券横幅 */}
        <div className={cn(
          "px-3 py-2 overflow-hidden",
          isCollapsed && "px-2"
        )}>
          <SidebarCouponBanner isCollapsed={isCollapsed} />
        </div>

        {/* 灵活空间区域 */}
        {!isCollapsed && <div className="flex-grow" />}

        {/* 底部导航和版权 */}
        <div className="px-3 mb-7">
          {/* 底部导航项 */}
          <div className="space-y-2 my-4 border-t border-border pt-5 dark:border-gray-800">
            {bottomNavItems.map(renderNavItem)}
          </div>

          {/* 社交媒体链接 */}
          {!isCollapsed && renderSocialLinks()}
        </div>

        {/* 版权信息 */}
        {!isCollapsed && (
          <div
            className={cn(
              "text-center text-xs py-2",
              pathname.startsWith("/train")
                ? "text-[#F5EFFF]/50 font-mono"
                : "text-muted-foreground"
            )}
          >
            <p>© 2025 ReelMind</p>
          </div>
        )}
      </motion.aside>

      {/* 移动底部导航栏 */}
      <div className="lg:hidden">
        <MobileNavBar />
      </div>
    </>
  );
}
