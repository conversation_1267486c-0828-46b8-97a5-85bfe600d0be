import "./instrument";

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as bodyParser from 'body-parser';
import { STRIPE_WEBHOOK_MODULE, STRIPE_WEBHOOK_ROUTE } from './common/constants/route';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bodyParser: false,  // 禁用内置的bodyParser
  });

  const stripeWebhookRoute = `${STRIPE_WEBHOOK_MODULE}${STRIPE_WEBHOOK_ROUTE}`;

  // 为Stripe webhook路由配置原始请求体处理
  app.use(stripeWebhookRoute,
    bodyParser.raw({ type: 'application/json' })
  );

  // 为其他路由配置JSON解析
  app.use((req, res, next) => {
    if (req.url.includes(stripeWebhookRoute)) {
      next();
    } else {
      bodyParser.json()(req, res, next);
    }
  });

  // 添加CORS配置
  app.enableCors({
    origin: ['http://localhost:3001', 'http://localhost:3000', 'http://localhost:5173', 'https://*.reelmind.ai'],
    methods: ['GET', 'POST', 'PUT', 'OPTIONS'],
    credentials: true,
  });

  app.useGlobalPipes(new ValidationPipe({
    // whitelist: true, // 去除未定义的属性
    transform: true, // 自动转换类型
    // forbidNonWhitelisted: true, // 禁止未定义的属性
    transformOptions: {
      enableImplicitConversion: true, // 启用隐式转换
    },
  }));

  // 配置 Swagger 文档
  const config = new DocumentBuilder()
    .setTitle('ReelMind API')
    .setDescription('ReelMind 平台 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(process.env.PORT ?? 3000);

  console.log('process.env =', process.env.NODE_ENV);
  console.log(`Application is running on: http://localhost:${process.env.PORT ?? 3000}`);
  console.log(`Swagger documentation is available at: http://localhost:${process.env.PORT ?? 3000}/api/docs`);
}

bootstrap();
