"use client"

import { useState, useRef, useEffect } from "react"
import { BillingToggle } from "../membership/components/billing-toggle"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Check, Code, CreditCard, Crown, ExternalLink, Eye, EyeOff, ChevronRight, Sparkles, Globe, Loader2, Trash2 } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import Image from "next/image"
import { apiService, ApiKey, MembershipUsage, CreditBalance, CreditTransaction } from "./api-service"
import { useToast } from "@/components/ui/toast"
import { Button } from "@/components/ui/button"
import { CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { ArrowLeft, ChevronDown } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

export default function ApiPlatformPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("yearly")
  const [activeTab, setActiveTab] = useState<string>("documentation")
  const [showSecretKey, setShowSecretKey] = useState(false)
  const { user } = useAuth()
  const { success, error } = useToast()

  // API data states
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])
  const [selectedApiKey, setSelectedApiKey] = useState<ApiKey | null>(null)
  const [membershipUsage, setMembershipUsage] = useState<MembershipUsage | null>(null)
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null)
  const [isLoadingKeys, setIsLoadingKeys] = useState(false)
  const [isLoadingMembership, setIsLoadingMembership] = useState(false)
  const [isLoadingCredits, setIsLoadingCredits] = useState(false)
  const [isCreatingKey, setIsCreatingKey] = useState(false)
  const [newKeyName, setNewKeyName] = useState("")
  const [newKeyDescription, setNewKeyDescription] = useState("")
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [keyToDelete, setKeyToDelete] = useState<ApiKey | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const pricingRef = useRef<HTMLDivElement>(null)
  const documentationRef = useRef<HTMLDivElement>(null)

  // Handle button click to scroll to corresponding section
  const scrollToSection = (section: string) => {
    setActiveTab(section)

    // Use setTimeout to ensure Tab content is rendered
    setTimeout(() => {
      if (section === "pricing" && pricingRef.current) {
        pricingRef.current.scrollIntoView({ behavior: "smooth" })
      } else if (section === "documentation" && documentationRef.current) {
        documentationRef.current.scrollIntoView({ behavior: "smooth" })
      }
    }, 100)
  }

  // Get API key list
  const fetchApiKeys = async () => {
    if (!user) return;

    try {
      setIsLoadingKeys(true);
      const keys = await apiService.getApiKeys();
      console.log('API keys retrieved:', keys);

      // Ensure we have an array of API keys
      if (Array.isArray(keys)) {
        setApiKeys(keys);
        if (keys.length > 0) {
          setSelectedApiKey(keys[0]);
        } else {
          setSelectedApiKey(null);
        }
      } else {
        console.error('Invalid API keys response format:', keys);
        setApiKeys([]);
        setSelectedApiKey(null);
        error('Failed to fetch API keys', 'Invalid response format');
      }
    } catch (err) {
      console.error('Failed to fetch API keys:', err);
      error('Failed to fetch API keys', (err as Error).message);
      setApiKeys([]);
      setSelectedApiKey(null);
    } finally {
      setIsLoadingKeys(false);
    }
  };

  // Create API key
  const createApiKey = async (name: string, description: string = "") => {
    if (!name.trim()) {
      error("API key name is required");
      return;
    }

    setIsCreatingKey(true);
    try {
      const newKey = await apiService.createApiKey(name, description);
      setApiKeys(prev => [newKey, ...prev]);
      setSelectedApiKey(newKey);
      setNewKeyName("");
      setNewKeyDescription("");
      success("API key created successfully");
    } catch (err) {
      console.error("Error creating API key:", err);
      error("Failed to create API key", (err as Error).message);
    } finally {
      setIsCreatingKey(false);
    }
  };

  // Delete API key
  const deleteApiKey = async (id: string) => {
    if (!user) return

    try {
      setIsDeleting(true)
      await apiService.deleteApiKey(id)
      setApiKeys(apiKeys.filter(key => key.id !== id))
      if (selectedApiKey?.id === id) {
        setSelectedApiKey(apiKeys.length > 1 ? apiKeys.find(key => key.id !== id) || null : null)
      }
      success("API key deleted successfully", "API key has been deleted")
    } catch (err) {
      console.error("Failed to delete API key:", err)
      error("Failed to delete API key", (err as Error).message)
    } finally {
      setShowDeleteModal(false)
      setIsDeleting(false)
    }
  }

  // Get membership usage information
  const fetchMembershipUsage = async () => {
    if (!user) return

    try {
      setIsLoadingMembership(true)
      const usage = await apiService.getMembershipUsage()
      setMembershipUsage(usage)
    } catch (err) {
      console.error("Failed to get membership usage:", err)
      error("Failed to get membership usage", (err as Error).message)
    } finally {
      setIsLoadingMembership(false)
    }
  }

  // Get credit balance
  const fetchCreditBalance = async () => {
    if (!user) return

    try {
      setIsLoadingCredits(true)
      const balance = await apiService.getCreditBalance()
      setCreditBalance(balance)
    } catch (err) {
      console.error("Failed to get credit balance:", err)
      error("Failed to get credit balance", (err as Error).message)
    } finally {
      setIsLoadingCredits(false)
    }
  }

  // Initial data loading
  useEffect(() => {
    if (user) {
      fetchApiKeys()
      fetchMembershipUsage()
      fetchCreditBalance()
    }
  }, [user])

  // API pricing plans
  const apiPlans = [
    {
      name: "Free",
      price: 0,
      description: "For individuals who wants to try out the most advanced AI video generation",
      features: [
        "Can purchase credits",
        "1 effect available",
        "1 Concurrency limit",
        "Supports high resolution up to 540P"
      ],
      highlight: false,
      buttonText: "Your current plan"
    },
    {
      name: "Essential",
      price: 100,
      description: "For creators ramping up their content production",
      features: [
        "15,000 Credits per month",
        "333 5s videos",
        "3 effect available",
        "5 Concurrency limit",
        "Supports high resolution up to 1080P"
      ],
      highlight: false,
      buttonText: "Purchase"
    },
    {
      name: "Scale",
      price: 1500,
      description: "For growing companies with higher demands",
      features: [
        "239,230 Credits per month",
        "5,316 5s videos",
        "5 effect available",
        "10 Concurrency limit",
        "Supports high resolution up to 1080P"
      ],
      highlight: true,
      buttonText: "Purchase"
    },
    {
      name: "Business",
      price: 6000,
      description: "For growing companies with higher demands",
      features: [
        "1,069,500 Credits per month",
        "23,766 5s videos",
        "10 effect available",
        "15 Concurrency limit",
        "Supports high resolution up to 1080P"
      ],
      highlight: false,
      buttonText: "Purchase"
    },
    {
      name: "Custom Plan",
      price: null,
      description: "For enterprise's that need volume based discounts and custom terms.",
      features: [
        "API access to everything",
        "Scalable pricing with volume based discounts",
        "More effects or customized effects",
        "More concurrency limit"
      ],
      highlight: false,
      buttonText: "Let's talk"
    }
  ]

  // Sample API endpoints for documentation
  const apiEndpoints = [
    {
      name: "Get Account Balance",
      endpoint: "/openapi/v2/account/balance",
      method: "GET",
      description: "Get the current account credit balance information",
      parameters: []
    },
    {
      name: "Upload Base64 Image",
      endpoint: "/openapi/v2/image/upload",
      method: "POST",
      description: "Upload a Base64 encoded image for video generation",
      parameters: [
        { name: "image_base64", type: "string", required: true, description: "Base64 encoded image data, must start with 'data:image/xxx;base64,'" },
        { name: "file_name", type: "string", required: false, description: "Image filename (optional)" }
      ]
    },
    {
      name: "Upload Image from URL",
      endpoint: "/openapi/v2/image/upload_url",
      method: "POST",
      description: "Upload an image from a specified URL for video generation",
      parameters: [
        { name: "image_url", type: "string", required: true, description: "URL of the image" }
      ]
    },
    {
      name: "Text to Video Generation",
      endpoint: "/openapi/v2/video/text/generate",
      method: "POST",
      description: "Generate a video based on text prompt",
      parameters: [
        { name: "prompt", type: "string", required: true, description: "Text prompt describing the video content to generate" },
        { name: "negative_prompt", type: "string", required: false, description: "Negative prompt describing what to avoid in the video" },
        { name: "quality", type: "string", required: true, description: "Video quality, available options: 'sd', 'hd', 'full_hd'" },
        { name: "duration", type: "number", required: true, description: "Video duration in seconds, typically 5 or 8" },
        { name: "aspect_ratio", type: "string", required: true, description: "Video aspect ratio, e.g. '16:9', '1:1', '9:16'" },
        { name: "motion_mode", type: "string", required: false, description: "Video motion mode, available options: 'normal', 'fast'" },
        { name: "style", type: "string", required: false, description: "Video style" },
        { name: "seed", type: "number", required: false, description: "Random seed for reproducibility" },
        { name: "water_mark", type: "boolean", required: false, description: "Whether to add a watermark" }
      ]
    },
    {
      name: "Image to Video Generation",
      endpoint: "/openapi/v2/video/img/generate",
      method: "POST",
      description: "Generate a video based on an image and text prompt",
      parameters: [
        { name: "prompt", type: "string", required: true, description: "Text prompt describing the video content to generate" },
        { name: "negative_prompt", type: "string", required: false, description: "Negative prompt describing what to avoid in the video" },
        { name: "quality", type: "string", required: true, description: "Video quality, available options: 'sd', 'hd', 'full_hd'" },
        { name: "duration", type: "number", required: true, description: "Video duration in seconds, typically 5 or 8" },
        { name: "aspect_ratio", type: "string", required: true, description: "Video aspect ratio, e.g. '16:9', '1:1', '9:16'" },
        { name: "motion_mode", type: "string", required: false, description: "Video motion mode, available options: 'normal', 'fast'" },
        { name: "style", type: "string", required: false, description: "Video style" },
        { name: "seed", type: "number", required: false, description: "Random seed for reproducibility" },
        { name: "water_mark", type: "boolean", required: false, description: "Whether to add a watermark" },
        { name: "img_id", type: "number", required: true, description: "ID of the uploaded image, obtained from image upload API" }
      ]
    },
    {
      name: "Get Video Generation Result",
      endpoint: "/openapi/v2/video/result/{id}",
      method: "GET",
      description: "Get the result of a video generation task",
      parameters: [
        { name: "id", type: "string", required: true, description: "ID of the video generation task" }
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Hero Section */}
      <div className="bg-gradient-to-b from-background to-background/80 border-b">
        <div className="max-w-6xl mx-auto px-5 py-12">
          {/* Enhanced Title Section - Fixed spacing */}
          <div className="text-center relative mb-10">
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary-foreground text-xs font-medium">
                <Globe className="h-3 w-3 mr-1" />
                <span>Developer Tools</span>
              </div>
            </div>

            <div className="inline-block relative mt-6">
              <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
                ReelMind API Platform
              </h1>
              <div className="absolute -top-1 -right-4 h-6 w-6 text-primary-foreground">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
                  <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor" opacity="0.2" />
                </svg>
              </div>
            </div>

            <p className="text-lg text-muted-foreground mt-4 max-w-3xl mx-auto">
              Turn your video assets into high-ROI creative revenue streams
            </p>

            <div className="absolute top-1/2 -translate-y-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-muted to-transparent opacity-30 -z-10"></div>

            <div className="flex flex-wrap justify-center gap-4 mt-8">
              <button
                onClick={() => scrollToSection("documentation")}
                className="px-6 py-3 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
              >
                View Documentation
              </button>
              <button
                onClick={() => scrollToSection("pricing")}
                className="px-6 py-3 rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/90 transition-colors"
              >
                See Pricing
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* API Key Information Card */}
      <div className="max-w-6xl mx-auto px-4 -mt-8 mb-8 relative z-10">
        <Card className="bg-white dark:bg-gray-800/90 shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden">
          <CardContent className="p-0">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-6">
              <div className="flex items-center gap-4">
                <div className="h-16 w-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <span className="text-xl font-bold">
                    {user && user.email ? user.email.charAt(0).toUpperCase() : "L"}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-bold">{user?.email || "Not Logged In"}</h2>
                  {isLoadingKeys ? (
                    <div className="mt-2 flex items-center">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span className="text-sm text-muted-foreground">Loading API keys...</span>
                    </div>
                  ) : selectedApiKey ? (
                    <div className="mt-1 space-y-1">
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 dark:text-gray-400 w-24">AccessKey:</span>
                        <span className="font-mono">{selectedApiKey.key}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 dark:text-gray-400 w-24">SecretKey:</span>
                        <div className="flex items-center">
                          <span className="font-mono">{showSecretKey ? selectedApiKey.key : '•'.repeat(24)}</span>
                          <button
                            onClick={() => setShowSecretKey(!showSecretKey)}
                            className="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          >
                            {showSecretKey ? <EyeOff size={16} /> : <Eye size={16} />}
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="mt-1 text-sm text-muted-foreground">No API keys available</p>
                  )}
                </div>
                <div className="ml-auto flex gap-3">
                  <button
                    onClick={() => {
                      const name = prompt("Enter API key name");
                      if (name) {
                        const description = prompt("Enter API key description (optional)", "");
                        setNewKeyName(name);
                        createApiKey(name, description || "");
                      }
                    }}
                    disabled={isCreatingKey}
                    className="px-3 py-1.5 text-sm border border-blue-500 text-blue-500 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20 flex items-center"
                  >
                    {isCreatingKey && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
                    Create API Key
                  </button>
                  {selectedApiKey && (
                    <button
                      onClick={() => {
                        setKeyToDelete(selectedApiKey)
                        setShowDeleteModal(true)
                      }}
                      className="px-3 py-1.5 text-sm border border-red-500 text-red-500 rounded hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete API Key
                    </button>
                  )}
                </div>
              </div>
              <div className="mt-3 text-sm text-gray-500 dark:text-gray-400">
                <span>If you need help, please check our </span>
                <button
                  onClick={() => scrollToSection("documentation")}
                  className="text-blue-500 hover:underline"
                >
                  API Documentation
                </button>
              </div>
            </div>

            <div className="grid grid-cols-4 divide-x divide-gray-100 dark:divide-gray-700">
              <div className="p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400">Plan</div>
                {isLoadingMembership ? (
                  <div className="mt-1 flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    <span>Loading...</span>
                  </div>
                ) : (
                  <div className="mt-1 font-medium">
                    {membershipUsage && membershipUsage.tier ?
                      membershipUsage.tier.charAt(0).toUpperCase() + membershipUsage.tier.slice(1) :
                      'Free Plan'}
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400">Reset Date</div>
                {isLoadingMembership ? (
                  <div className="mt-1 flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    <span>Loading...</span>
                  </div>
                ) : (
                  <div className="mt-1 font-medium">
                    {membershipUsage ? new Date(membershipUsage.reset_date).toLocaleDateString() : 'N/A'}
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400">Credits</div>
                {isLoadingCredits ? (
                  <div className="mt-1 flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    <span>Loading...</span>
                  </div>
                ) : (
                  <div className="mt-1 font-medium flex items-center">
                    {creditBalance ? creditBalance.total_credits : 0}
                    <button
                      onClick={() => scrollToSection("pricing")}
                      className="ml-2 text-blue-500 hover:underline flex items-center text-sm"
                    >
                      Buy Credits <ChevronRight size={14} />
                    </button>
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400">Tasks</div>
                <div className="mt-1 font-medium">
                  {membershipUsage && membershipUsage.tier === 'free' ? '1' :
                    membershipUsage && membershipUsage.tier === 'essential' ? '5' :
                      membershipUsage && membershipUsage.tier === 'scale' ? '10' :
                        membershipUsage && membershipUsage.tier === 'business' ? '15' : '1'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-12">
        <Tabs value={activeTab} className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="documentation">API Documentation</TabsTrigger>
            <TabsTrigger value="pricing">API Pricing</TabsTrigger>
          </TabsList>

          {/* Documentation Tab */}
          <TabsContent value="documentation" id="documentation">
            <div ref={documentationRef} className="mb-8">
              <h2 className="text-3xl font-bold mb-6">API Documentation</h2>
              <p className="text-muted-foreground mb-8">
                Integrate ReelMind's powerful video generation capabilities into your applications with our simple REST API.
              </p>

              {/* Run API Agent Button - Closed Beta */}
              <div className="mb-8">
                <div className="relative inline-block">
                  <button className="w-full flex items-center justify-center gap-3 px-6 py-4 border-2 border-dashed border-blue-400 dark:border-blue-600 rounded-lg bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                    <Code className="h-5 w-5 text-blue-500" />
                    <span className="font-medium text-blue-700 dark:text-blue-300">Run API Agent</span>
                    <Sparkles className="h-4 w-4 text-blue-500" />
                  </button>
                  <div className="absolute -top-3 -right-3 px-2 py-1 text-xs font-bold rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-md transform rotate-3">
                    Closed Beta
                  </div>
                </div>
                <p className="mt-3 text-sm text-muted-foreground">
                  Our AI-powered API Agent helps you build and test API requests without writing code.
                </p>
              </div>

              {/* Authentication Section */}
              <div className="mb-12">
                <h3 className="text-xl font-bold mb-4">Authentication</h3>
                <Card>
                  <CardContent className="p-6">
                    <p className="mb-4">All API requests require authentication using an API key. You can generate your API key on the API platform page.</p>
                    <div className="bg-muted p-4 rounded-md">
                      <code className="text-sm">
                        API-KEY: your_api_key_here<br />
                        ai-trace-id: unique_trace_id_here
                      </code>
                    </div>
                    <p className="mt-4 text-sm text-muted-foreground">
                      <strong>API-KEY</strong>: Your API key for authentication<br />
                      <strong>ai-trace-id</strong>: A unique trace ID generated by you for request identification and debugging
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Endpoints Section */}
              <div>
                <h3 className="text-xl font-bold mb-4">API Endpoints</h3>

                {/* API Response Format Section */}
                <Card className="mb-6">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-bold">Response Format</h4>
                    </div>
                    <p className="mb-4 text-muted-foreground">All API responses follow this standard format:</p>
                    <div className="bg-muted p-3 rounded-md mb-4">
                      <code className="text-sm">
                        {`{
  "video_id": 12345,  // or other response data
  ...
}`}
                      </code>
                    </div>

                    <h5 className="font-semibold mb-2 mt-4">Error Handling</h5>
                    <p className="mb-4 text-muted-foreground">The API uses standard HTTP status codes to indicate the result of requests:</p>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2 px-2">Status Code</th>
                            <th className="text-left py-2 px-2">Description</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr className="border-b">
                            <td className="py-2 px-2">200 OK</td>
                            <td className="py-2 px-2">Request successful</td>
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-2">400 Bad Request</td>
                            <td className="py-2 px-2">Invalid request parameters</td>
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-2">401 Unauthorized</td>
                            <td className="py-2 px-2">Invalid or missing API key</td>
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-2">402 Payment Required</td>
                            <td className="py-2 px-2">Insufficient credit balance</td>
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-2">404 Not Found</td>
                            <td className="py-2 px-2">Resource not found</td>
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-2">429 Too Many Requests</td>
                            <td className="py-2 px-2">Request rate limit exceeded</td>
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-2">500 Internal Server Error</td>
                            <td className="py-2 px-2">Server error</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>

                {/* API Endpoints */}
                <div className="space-y-6">
                  {apiEndpoints.map((endpoint, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-lg font-bold">{endpoint.name}</h4>
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="font-mono">
                              {endpoint.method}
                            </Badge>
                          </div>
                        </div>
                        <p className="mb-6 text-muted-foreground">{endpoint.description}</p>
                        <div className="bg-muted p-3 rounded-md mb-6">
                          <code className="text-sm font-mono">
                            {endpoint.endpoint}
                          </code>
                        </div>

                        {endpoint.parameters.length > 0 && (
                          <div>
                            <h5 className="font-semibold mb-3">Parameters</h5>
                            <div className="overflow-x-auto">
                              <table className="w-full text-sm">
                                <thead>
                                  <tr className="border-b">
                                    <th className="text-left py-2 px-2">Name</th>
                                    <th className="text-left py-2 px-2">Type</th>
                                    <th className="text-left py-2 px-2">Required</th>
                                    <th className="text-left py-2 px-2">Description</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {endpoint.parameters.map((param, i) => (
                                    <tr key={i} className="border-b">
                                      <td className="py-2 px-2 font-mono">{param.name}</td>
                                      <td className="py-2 px-2">{param.type}</td>
                                      <td className="py-2 px-2">{param.required ? "Yes" : "No"}</td>
                                      <td className="py-2 px-2">{param.description}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Code Examples Section */}
              <div className="mt-12">
                <h3 className="text-xl font-bold mb-4">Code Examples</h3>

                {/* Node.js Example */}
                <Card className="mb-6">
                  <CardContent className="p-6">
                    <h4 className="text-lg font-bold mb-4">Node.js Example</h4>
                    <div className="bg-muted p-3 rounded-md mb-4 overflow-auto max-h-[400px]">
                      <code className="text-sm whitespace-pre">
                        {`const axios = require('axios');

// API Configuration
const API_KEY = 'your_api_key_here'; // Replace with your API key
const API_BASE_URL = 'https://api.reelmind.ai/openapi/v2';
const TRACE_ID = \`trace_\${Date.now()}\`; // Create a unique trace ID

// Create API client
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'API-KEY': API_KEY,
    'ai-trace-id': TRACE_ID,
    'Content-Type': 'application/json'
  }
});

// Text to video generation example
async function generateVideoFromText() {
  try {
    const response = await apiClient.post('/video/text/generate', {
      prompt: "A beautiful beach sunset scene with waves gently lapping the shore",
      quality: "hd",
      duration: 5,
      aspect_ratio: "16:9",
      motion_mode: "normal"
    });
    
    console.log('Video generation task submitted:', response.data);
    const videoId = response.data.video_id;
    
    // Get video generation result
    setTimeout(async () => {
      const resultResponse = await apiClient.get(\`/video/result/\${videoId}\`);
      console.log('Video generation result:', resultResponse.data);
    }, 10000); // Wait 10 seconds before checking the result
    
    return response.data;
  } catch (error) {
    console.error('Video generation task submission failed:', error.response ? error.response.data : error.message);
  }
}

// Run the example
generateVideoFromText();`}
                      </code>
                    </div>
                  </CardContent>
                </Card>

                {/* Python Example */}
                <Card>
                  <CardContent className="p-6">
                    <h4 className="text-lg font-bold mb-4">Python Example</h4>
                    <div className="bg-muted p-3 rounded-md mb-4 overflow-auto max-h-[400px]">
                      <code className="text-sm whitespace-pre">
                        {`import requests
import time

# API Configuration
API_KEY = 'your_api_key_here'  # Replace with your API key
API_BASE_URL = 'https://api.reelmind.ai/openapi/v2'
TRACE_ID = f'trace_{int(time.time() * 1000)}'  # Create a unique trace ID

headers = {
    'API-KEY': API_KEY,
    'ai-trace-id': TRACE_ID,
    'Content-Type': 'application/json'
}

# Upload image from URL
def upload_image_from_url(image_url):
    try:
        response = requests.post(
            f'{API_BASE_URL}/image/upload_url',
            headers=headers,
            json={
                'image_url': image_url
            }
        )
        response.raise_for_status()
        print('Image upload successful:', response.json())
        return response.json()
    except requests.exceptions.RequestException as e:
        print('Image upload failed:', e)
        return None

# Image to video generation
def generate_video_from_image(img_id):
    try:
        response = requests.post(
            f'{API_BASE_URL}/video/img/generate',
            headers=headers,
            json={
                'prompt': 'A beautiful beach sunset scene with waves gently lapping the shore',
                'quality': 'hd',
                'duration': 5,
                'aspect_ratio': '16:9',
                'img_id': img_id
            }
        )
        response.raise_for_status()
        print('Video generation task submitted:', response.json())
        return response.json()
    except requests.exceptions.RequestException as e:
        print('Video generation task submission failed:', e)
        return None

# Get video generation result
def get_video_result(video_id):
    try:
        response = requests.get(
            f'{API_BASE_URL}/video/result/{video_id}',
            headers=headers
        )
        response.raise_for_status()
        print('Video generation result:', response.json())
        return response.json()
    except requests.exceptions.RequestException as e:
        print('Failed to get video generation result:', e)
        return None

# Run complete example
def run_example():
    # Upload image
    image_result = upload_image_from_url('https://example.com/image.jpg')
    if image_result and 'img_id' in image_result:
        # Generate video using the uploaded image
        video_result = generate_video_from_image(image_result['img_id'])
        if video_result and 'video_id' in video_result:
            # Wait a while then check the video generation result
            print('Waiting for video generation...')
            time.sleep(10)  # Wait 10 seconds
            get_video_result(video_result['video_id'])

# Run the example
run_example()`}
                      </code>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Pricing Tab */}
          <TabsContent value="pricing" id="pricing">
            <div ref={pricingRef} className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-6">Find the right plan</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
                Turn your video assets into high-ROI creative revenue streams
              </p>

              {/* Billing Toggle */}
              <BillingToggle billingCycle={billingCycle} onChange={(cycle) => setBillingCycle(cycle)} />
            </div>

            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-16">
              {apiPlans.map((plan, index) => (
                <Card
                  key={index}
                  className={`overflow-hidden ${plan.highlight ? 'border-primary shadow-lg' : 'border-border'}`}
                >
                  <CardContent className="p-0">
                    <div className={`p-6 ${plan.highlight ? 'bg-primary/5' : ''}`}>
                      <h3 className={`text-xl font-bold mb-2 ${plan.highlight ? 'text-primary' : ''}`}>
                        {plan.name}
                      </h3>
                      <div className="mb-4">
                        {plan.price !== null ? (
                          <div className="flex items-baseline">
                            <span className="text-3xl font-bold">${plan.price}</span>
                            <span className="text-muted-foreground ml-1">/ month</span>
                          </div>
                        ) : (
                          <div className="flex items-baseline">
                            <span className="text-3xl font-bold">Custom pricing</span>
                          </div>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-6">
                        {plan.description}
                      </p>
                      <button
                        className={`w-full py-2 rounded-md transition-colors ${plan.name === "Free"
                          ? "bg-secondary text-secondary-foreground cursor-default"
                          : plan.name === "Custom Plan"
                            ? "bg-white text-black border border-gray-300 hover:bg-gray-100"
                            : "bg-primary text-primary-foreground hover:bg-primary/90"
                          }`}
                      >
                        {plan.buttonText}
                      </button>
                    </div>
                    <div className="p-6 border-t">
                      <ul className="space-y-3">
                        {plan.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Credits and Rate Limits Section */}
            <div className="mt-12">
              <h3 className="text-xl font-bold mb-4">Credit Consumption and Rate Limits</h3>

              {/* Credits Consumption */}
              <Card className="mb-6">
                <CardContent className="p-6">
                  <h4 className="text-lg font-bold mb-4">Credit Consumption</h4>
                  <p className="mb-4 text-muted-foreground">Different operations consume different amounts of credits:</p>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-2">Operation</th>
                          <th className="text-left py-2 px-2">Credit Consumption</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-2 px-2">Standard Definition (SD) 5-second video</td>
                          <td className="py-2 px-2">45 credits</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">Standard Definition (SD) 8-second video</td>
                          <td className="py-2 px-2">90 credits</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">High Definition (HD) 5-second video</td>
                          <td className="py-2 px-2">60 credits</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">High Definition (HD) 8-second video</td>
                          <td className="py-2 px-2">120 credits</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">Full High Definition (Full HD) 5-second video</td>
                          <td className="py-2 px-2">120 credits</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">Using fast motion mode</td>
                          <td className="py-2 px-2">Double credit consumption</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Rate Limits */}
              <Card>
                <CardContent className="p-6">
                  <h4 className="text-lg font-bold mb-4">Rate Limits</h4>
                  <p className="mb-4 text-muted-foreground">API requests are subject to rate limits based on your subscription plan:</p>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-2">Membership Tier</th>
                          <th className="text-left py-2 px-2">Rate Limit</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-2 px-2">Free Plan</td>
                          <td className="py-2 px-2">10 requests per minute</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">Essential Plan</td>
                          <td className="py-2 px-2">30 requests per minute</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">Scale Plan</td>
                          <td className="py-2 px-2">60 requests per minute</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-2">Business Plan</td>
                          <td className="py-2 px-2">120 requests per minute</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>

      {/* Footer */}
      <footer className="border-t border-border py-8 text-center text-muted-foreground">
        <p>© {new Date().getFullYear()} ReelMind. All rights reserved.</p>
      </footer>

      {/* Delete API Key Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this API key? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 mb-4">
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm font-medium">Key Name: {keyToDelete?.name}</p>
              <p className="text-sm text-muted-foreground mt-1">
                Created on {keyToDelete?.created_at ? new Date(keyToDelete.created_at).toLocaleDateString() : ''}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => keyToDelete?.id && deleteApiKey(keyToDelete.id)}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete API Key"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 